import apiClient from './api';
import { mockDataService } from './mockData';
import toast from 'react-hot-toast';

// Use mock service in development mode
const USE_MOCK = false;

// Audio file validation helper
const validateAudioFile = (file) => {
  const allowedTypes = ['audio/mpeg', 'audio/wav', 'audio/ogg', 'audio/m4a'];
  const maxSize = 10 * 1024 * 1024; // 10MB

  if (!allowedTypes.includes(file.type)) {
    throw new Error('Неподдерживаемый формат аудио файла. Поддерживаются: MP3, WAV, OGG, M4A');
  }

  if (file.size > maxSize) {
    throw new Error('Размер аудио файла не должен превышать 10MB');
  }

  return true;
};

export const contentService = {
  // Words management
  words: {
    async getAll(params = {}) {
      try {
        if (USE_MOCK) {
          return await mockDataService.getWords();
        }

        const queryParams = new URLSearchParams(params);
        const response = await apiClient.get(`/word?${queryParams}`);

        // Handle different possible response structures
        const responseData = response.data;

        if (responseData && responseData.words) {
          return responseData.words;
        }

        if (Array.isArray(responseData)) {
          return responseData;
        }

        if (responseData && responseData.data && Array.isArray(responseData.data)) {
          return responseData.data;
        }

        console.warn('Words API returned unexpected structure:', responseData);
        return [];
      } catch (error) {
        console.error('Error fetching words:', error);
        toast.error('Ошибка загрузки слов');
        return [];
      }
    },

    async getById(wordId) {
      try {
        if (USE_MOCK) {
          const words = await mockDataService.getWords();
          return words.find(word => word.id === wordId);
        }

        const response = await apiClient.get(`/word/${wordId}`);
        return response.data.word;
      } catch (error) {
        toast.error('Ошибка загрузки слова');
        throw error;
      }
    },

    async create(wordData, audioFile = null) {
      try {
        if (USE_MOCK) {
          // Mock creation
          const newWord = {
            id: Date.now(),
            ...wordData,
            created_at: new Date().toISOString()
          };
          toast.success('Слово создано успешно');
          return newWord;
        }

        // Debug logging
        console.log('🎵 Creating word with data:', wordData);
        console.log('🎵 Audio file:', audioFile);
        console.log('🎵 Audio file type:', audioFile?.type);
        console.log('🎵 Audio file size:', audioFile?.size);
        console.log('🎵 Audio file name:', audioFile?.name);

        // Always use the with-audio endpoint
        if (audioFile) {
          // Validate audio file if provided
          validateAudioFile(audioFile);
          console.log('🎵 Audio file validation passed');
        }

        const formData = new FormData();
        formData.append('kaz_plaintext', wordData.kaz_plaintext);
        formData.append('rus_plaintext', wordData.rus_plaintext);

        // Add audio file if provided (optional)
        if (audioFile) {
          formData.append('audio', audioFile);
          console.log('🎵 Audio file added to FormData');
        }

        // Debug FormData contents
        console.log('🎵 FormData contents:');
        for (let [key, value] of formData.entries()) {
          console.log(`  ${key}:`, value);
        }

        const response = await apiClient.post('/word/with-audio', formData);

        console.log('🎵 Server response:', response.data);
        toast.success('Слово создано успешно');
        return response.data.word;
      } catch (error) {
        console.error('🎵 Error creating word:', error);
        toast.error('Ошибка создания слова');
        throw error;
      }
    },

    async update(wordId, wordData, audioFile = null) {
      try {
        if (USE_MOCK) {
          // Mock update
          const updatedWord = {
            id: wordId,
            ...wordData,
            updated_at: new Date().toISOString()
          };
          toast.success('Слово обновлено успешно');
          return updatedWord;
        }

        // Always use the with-audio endpoint for updates too
        if (audioFile) {
          // Validate audio file if provided
          validateAudioFile(audioFile);
        }

        const formData = new FormData();
        formData.append('kaz_plaintext', wordData.kaz_plaintext);
        formData.append('rus_plaintext', wordData.rus_plaintext);

        // Add audio file if provided (optional)
        if (audioFile) {
          formData.append('audio', audioFile);
        }

        const response = await apiClient.put(`/word/with-audio/${wordId}`, formData);

        toast.success('Слово обновлено успешно');
        return response.data.word;
      } catch (error) {
        toast.error('Ошибка обновления слова');
        throw error;
      }
    },

    async delete(wordId) {
      try {
        if (USE_MOCK) {
          // Mock deletion
          toast.success('Слово удалено успешно');
          return { message: 'Word deleted successfully' };
        }

        const response = await apiClient.delete(`/word/${wordId}`);
        toast.success('Слово удалено успешно');
        return response.data;
      } catch (error) {
        toast.error('Ошибка удаления слова');
        throw error;
      }
    }
  },

  // Sentences management
  sentences: {
    async getAll(params = {}) {
      try {
        if (USE_MOCK) {
          return await mockDataService.getSentences();
        }

        const queryParams = new URLSearchParams(params);
        const response = await apiClient.get(`/sentence/all?${queryParams}`);

        // Handle different possible response structures
        const responseData = response.data;

        if (responseData && responseData.sentences) {
          return responseData.sentences;
        }

        if (Array.isArray(responseData)) {
          return responseData;
        }

        if (responseData && responseData.data && Array.isArray(responseData.data)) {
          return responseData.data;
        }

        console.warn('Sentences API returned unexpected structure:', responseData);
        return [];
      } catch (error) {
        console.error('Error fetching sentences:', error);
        toast.error('Ошибка загрузки предложений');
        return [];
      }
    },

    async getById(sentenceId) {
      try {
        if (USE_MOCK) {
          const sentences = await mockDataService.getSentences();
          return sentences.find(sentence => sentence.id === sentenceId);
        }

        const response = await apiClient.get(`/sentence?id=${sentenceId}`);
        return response.data.sentence;
      } catch (error) {
        toast.error('Ошибка загрузки предложения');
        throw error;
      }
    },

    async create(sentenceData, audioFile = null) {
      try {
        if (USE_MOCK) {
          const newSentence = {
            id: Date.now(),
            ...sentenceData,
            created_at: new Date().toISOString()
          };
          toast.success('Предложение создано успешно');
          return newSentence;
        }

        // Debug logging
        console.log('🎵 Creating sentence with data:', sentenceData);
        console.log('🎵 Audio file:', audioFile);

        // Always use the with-audio endpoint
        if (audioFile) {
          // Validate audio file if provided
          validateAudioFile(audioFile);
          console.log('🎵 Audio file validation passed');
        }

        const formData = new FormData();
        formData.append('kaz_plaintext', sentenceData.kaz_plaintext);
        formData.append('rus_plaintext', sentenceData.rus_plaintext);

        // Add audio file if provided (optional)
        if (audioFile) {
          formData.append('audio', audioFile);
          console.log('🎵 Audio file added to FormData');
        }

        // Debug FormData contents
        console.log('🎵 FormData contents:');
        for (let [key, value] of formData.entries()) {
          console.log(`  ${key}:`, value);
        }

        const response = await apiClient.post('/sentence/with-audio', formData);

        toast.success('Предложение создано успешно');
        return response.data.sentence;
      } catch (error) {
        toast.error('Ошибка создания предложения');
        throw error;
      }
    },

    async update(sentenceId, sentenceData, audioFile = null) {
      try {
        if (USE_MOCK) {
          const updatedSentence = {
            id: sentenceId,
            ...sentenceData,
            updated_at: new Date().toISOString()
          };
          toast.success('Предложение обновлено успешно');
          return updatedSentence;
        }

        // Always use the with-audio endpoint for updates too
        if (audioFile) {
          // Validate audio file if provided
          validateAudioFile(audioFile);
        }

        const formData = new FormData();
        formData.append('kaz_plaintext', sentenceData.kaz_plaintext);
        formData.append('rus_plaintext', sentenceData.rus_plaintext);

        // Add audio file if provided (optional)
        if (audioFile) {
          formData.append('audio', audioFile);
        }

        const response = await apiClient.put(`/sentence/with-audio/${sentenceId}`, formData);

        toast.success('Предложение обновлено успешно');
        return response.data.sentence;
      } catch (error) {
        toast.error('Ошибка обновления предложения');
        throw error;
      }
    },

    async delete(sentenceId) {
      try {
        if (USE_MOCK) {
          toast.success('Предложение удалено успешно');
          return { message: 'Sentence deleted successfully' };
        }

        const response = await apiClient.delete(`/sentence/${sentenceId}`);
        toast.success('Предложение удалено успешно');
        return response.data;
      } catch (error) {
        toast.error('Ошибка удаления предложения');
        throw error;
      }
    }
  },

  // Questions management
  questions: {
    async getAll(params = {}) {
      try {
        if (USE_MOCK) {
          return await mockDataService.getQuestions();
        }

        const queryParams = new URLSearchParams(params);
        const response = await apiClient.get(`/questions/all?${queryParams}`);

        // Handle different possible response structures
        const responseData = response.data;

        // Check if response has questions array
        if (responseData && responseData.questions) {
          return responseData.questions;
        }

        // Check if response data is directly an array
        if (Array.isArray(responseData)) {
          return responseData;
        }

        // Check if response has data wrapper
        if (responseData && responseData.data && Array.isArray(responseData.data)) {
          return responseData.data;
        }

        // If no questions found, return empty array
        console.warn('Questions API returned unexpected structure:', responseData);
        return [];
      } catch (error) {
        console.error('Error fetching questions:', error);
        toast.error('Ошибка загрузки вопросов');
        // Return empty array instead of throwing to prevent undefined
        return [];
      }
    },

    async getById(questionId) {
      try {
        if (USE_MOCK) {
          const questions = await mockDataService.getQuestions();
          return questions.find(question => question.id === questionId);
        }

        const response = await apiClient.get(`/questions?id=${questionId}`);
        const responseData = response.data;

        // Handle different possible response structures
        if (responseData && responseData.question) {
          return responseData.question;
        }

        if (responseData && responseData.data) {
          return responseData.data;
        }

        return responseData;
      } catch (error) {
        console.error('Error fetching question by ID:', error);
        toast.error('Ошибка загрузки вопроса');
        throw error;
      }
    },

    async create(questionData) {
      try {
        if (USE_MOCK) {
          const newQuestion = {
            id: Date.now(),
            ...questionData,
            created_at: new Date().toISOString()
          };
          toast.success('Вопрос создан успешно');
          return newQuestion;
        }

        const response = await apiClient.post('/questions', questionData);
        toast.success('Вопрос создан успешно');
        return response.data.question;
      } catch (error) {
        toast.error('Ошибка создания вопроса');
        throw error;
      }
    },

    async update(questionId, questionData) {
      try {
        if (USE_MOCK) {
          const updatedQuestion = {
            id: questionId,
            ...questionData,
            updated_at: new Date().toISOString()
          };
          toast.success('Вопрос обновлен успешно');
          return updatedQuestion;
        }

        const response = await apiClient.put(`/questions/${questionId}`, questionData);
        toast.success('Вопрос обновлен успешно');
        return response.data.question;
      } catch (error) {
        toast.error('Ошибка обновления вопроса');
        throw error;
      }
    },

    async delete(questionId) {
      try {
        if (USE_MOCK) {
          toast.success('Вопрос удален успешно');
          return { message: 'Question deleted successfully' };
        }

        const response = await apiClient.delete(`/questions/${questionId}`);
        toast.success('Вопрос удален успешно');
        return response.data;
      } catch (error) {
        toast.error('Ошибка удаления вопроса');
        throw error;
      }
    }
  },

  // Theories management
  theories: {
    async getAll(params = {}) {
      try {
        if (USE_MOCK) {
          return await mockDataService.getTheories();
        }

        const queryParams = new URLSearchParams(params);
        const response = await apiClient.get(`/theory/all?${queryParams}`);

        // Handle different possible response structures
        const responseData = response.data;

        if (responseData && responseData.theories) {
          return responseData.theories;
        }

        if (Array.isArray(responseData)) {
          return responseData;
        }

        if (responseData && responseData.data && Array.isArray(responseData.data)) {
          return responseData.data;
        }

        console.warn('Theories API returned unexpected structure:', responseData);
        return [];
      } catch (error) {
        console.error('Error fetching theories:', error);
        toast.error('Ошибка загрузки теорий');
        return [];
      }
    },

    async getById(theoryId) {
      try {
        if (USE_MOCK) {
          const theories = await mockDataService.getTheories();
          return theories.find(theory => theory.id === theoryId);
        }

        const response = await apiClient.get(`/theory?id=${theoryId}`);
        return response.data.theory;
      } catch (error) {
        toast.error('Ошибка загрузки теории');
        throw error;
      }
    },

    async create(theoryData) {
      try {
        if (USE_MOCK) {
          const newTheory = {
            id: Date.now(),
            ...theoryData,
            created_at: new Date().toISOString()
          };
          toast.success('Теория создана успешно');
          return newTheory;
        }

        const response = await apiClient.post('/theory', theoryData);
        toast.success('Теория создана успешно');
        return response.data.theory;
      } catch (error) {
        toast.error('Ошибка создания теории');
        throw error;
      }
    },

    async update(theoryId, theoryData) {
      try {
        if (USE_MOCK) {
          const updatedTheory = {
            id: theoryId,
            ...theoryData,
            updated_at: new Date().toISOString()
          };
          toast.success('Теория обновлена успешно');
          return updatedTheory;
        }

        const response = await apiClient.put('/theory', {
          id: theoryId,
          ...theoryData
        });
        toast.success('Теория обновлена успешно');
        return response.data.theory;
      } catch (error) {
        toast.error('Ошибка обновления теории');
        throw error;
      }
    },

    async delete(theoryId) {
      try {
        if (USE_MOCK) {
          toast.success('Теория удалена успешно');
          return { message: 'Theory deleted successfully' };
        }

        const response = await apiClient.delete(`/theory?id=${theoryId}`);
        toast.success('Теория удалена успешно');
        return response.data;
      } catch (error) {
        toast.error('Ошибка удаления теории');
        throw error;
      }
    }
  },

  // Modules management
  modules: {
    async getAll(params = {}) {
      try {
        if (USE_MOCK) {
          return await mockDataService.getModules();
        }

        const queryParams = new URLSearchParams(params);
        const response = await apiClient.get(`/module/all?${queryParams}`);

        // Handle different possible response structures
        const responseData = response.data;

        if (responseData && responseData.modules) {
          return responseData.modules;
        }

        if (Array.isArray(responseData)) {
          return responseData;
        }

        if (responseData && responseData.data && Array.isArray(responseData.data)) {
          return responseData.data;
        }

        console.warn('Modules API returned unexpected structure:', responseData);
        return [];
      } catch (error) {
        console.error('Error fetching modules:', error);
        toast.error('Ошибка загрузки модулей');
        return [];
      }
    },

    async getById(moduleId) {
      try {
        if (USE_MOCK) {
          const modules = await mockDataService.getModules();
          return modules.find(module => module.id === moduleId);
        }

        const response = await apiClient.get(`/module?id=${moduleId}`);
        return response.data.module;
      } catch (error) {
        toast.error('Ошибка загрузки модуля');
        throw error;
      }
    },

    async create(moduleData) {
      try {
        if (USE_MOCK) {
          const newModule = {
            id: Date.now(),
            ...moduleData,
            created_at: new Date().toISOString()
          };
          toast.success('Модуль создан успешно');
          return newModule;
        }

        const response = await apiClient.post('/module', moduleData);
        toast.success('Модуль создан успешно');
        return response.data.module;
      } catch (error) {
        toast.error('Ошибка создания модуля');
        throw error;
      }
    },

    async update(moduleId, moduleData) {
      try {
        if (USE_MOCK) {
          const updatedModule = {
            id: moduleId,
            ...moduleData,
            updated_at: new Date().toISOString()
          };
          toast.success('Модуль обновлен успешно');
          return updatedModule;
        }

        const response = await apiClient.put('/module', {
          id: moduleId,
          ...moduleData
        });
        toast.success('Модуль обновлен успешно');
        return response.data.module;
      } catch (error) {
        toast.error('Ошибка обновления модуля');
        throw error;
      }
    },

    async delete(moduleId) {
      try {
        if (USE_MOCK) {
          toast.success('Модуль удален успешно');
          return { message: 'Module deleted successfully' };
        }

        const response = await apiClient.delete(`/module?id=${moduleId}`);
        toast.success('Модуль удален успешно');
        return response.data;
      } catch (error) {
        toast.error('Ошибка удаления модуля');
        throw error;
      }
    },

    // Get user progress for a module
    async getUserProgress(userId) {
      try {
        if (USE_MOCK) {
          return await mockDataService.getUserProgress(userId);
        }

        const response = await apiClient.get(`/module-user-progress/${userId}`);
        return response.data;
      } catch (error) {
        toast.error('Ошибка загрузки прогресса пользователя');
        throw error;
      }
    }
  }
};

// Export validation function for use in components
export { validateAudioFile };

export default contentService;
