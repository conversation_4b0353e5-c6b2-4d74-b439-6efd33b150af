import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  FiDownload,
  FiRefreshCw,
  FiAlertTriangle,
  FiInfo,
  FiCheckCircle,
  FiX,
  FiClock,
  FiUser,
  FiServer
} from 'react-icons/fi';
import { formatDate } from '../../utils/helpers';

const LogEntry = ({ log, onExpand, isExpanded }) => {
  const getLevelIcon = (level) => {
    switch (level.toLowerCase()) {
      case 'error':
        return <FiAlertTriangle className="text-red-600" size={16} />;
      case 'warning':
        return <FiAlertTriangle className="text-yellow-600" size={16} />;
      case 'info':
        return <FiInfo className="text-blue-600" size={16} />;
      case 'success':
        return <FiCheckCircle className="text-green-600" size={16} />;
      default:
        return <FiInfo className="text-gray-600" size={16} />;
    }
  };

  const getLevelColor = (level) => {
    switch (level.toLowerCase()) {
      case 'error':
        return 'bg-red-50 border-red-200 text-red-800';
      case 'warning':
        return 'bg-yellow-50 border-yellow-200 text-yellow-800';
      case 'info':
        return 'bg-blue-50 border-blue-200 text-blue-800';
      case 'success':
        return 'bg-green-50 border-green-200 text-green-800';
      default:
        return 'bg-gray-50 border-gray-200 text-gray-800';
    }
  };

  const getSourceIcon = (source) => {
    switch (source.toLowerCase()) {
      case 'api':
        return <FiServer className="text-blue-600" size={14} />;
      case 'auth':
        return <FiUser className="text-purple-600" size={14} />;
      case 'system':
        return <FiServer className="text-gray-600" size={14} />;
      default:
        return <FiServer className="text-gray-600" size={14} />;
    }
  };

  return (
    <div className={`border rounded-lg p-4 transition-all ${getLevelColor(log.level)}`}>
      <div className="flex items-start gap-3">
        <div className="flex-shrink-0 mt-0.5">
          {getLevelIcon(log.level)}
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <span className="font-medium text-sm">{log.message}</span>
              {log.source && (
                <div className="flex items-center gap-1 px-2 py-1 bg-white bg-opacity-50 rounded text-xs">
                  {getSourceIcon(log.source)}
                  <span>{log.source}</span>
                </div>
              )}
            </div>
            <button
              onClick={() => onExpand(log.id)}
              className="text-xs text-gray-600 hover:text-gray-800"
            >
              {isExpanded ? 'Свернуть' : 'Подробнее'}
            </button>
          </div>
          
          <div className="flex items-center gap-4 text-xs text-gray-600">
            <div className="flex items-center gap-1">
              <FiClock size={12} />
              <span>{formatDate(log.timestamp)}</span>
            </div>
            {log.userId && (
              <div className="flex items-center gap-1">
                <FiUser size={12} />
                <span>User: {log.userId}</span>
              </div>
            )}
            {log.ip && (
              <span>IP: {log.ip}</span>
            )}
          </div>
          
          {isExpanded && (
            <div className="mt-3 pt-3 border-t border-current border-opacity-20">
              {log.details && (
                <div className="mb-3">
                  <h5 className="font-medium text-sm mb-1">Детали:</h5>
                  <pre className="text-xs bg-white bg-opacity-50 p-2 rounded overflow-x-auto">
                    {typeof log.details === 'string' ? log.details : JSON.stringify(log.details, null, 2)}
                  </pre>
                </div>
              )}
              
              {log.stackTrace && (
                <div className="mb-3">
                  <h5 className="font-medium text-sm mb-1">Stack Trace:</h5>
                  <pre className="text-xs bg-white bg-opacity-50 p-2 rounded overflow-x-auto">
                    {log.stackTrace}
                  </pre>
                </div>
              )}
              
              {log.context && (
                <div>
                  <h5 className="font-medium text-sm mb-1">Контекст:</h5>
                  <div className="text-xs space-y-1">
                    {Object.entries(log.context).map(([key, value]) => (
                      <div key={key} className="flex">
                        <span className="font-medium w-20">{key}:</span>
                        <span>{typeof value === 'object' ? JSON.stringify(value) : value}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

const SystemLogs = ({ logs = [], onRefresh, onExport, isLoading }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [levelFilter, setLevelFilter] = useState('');
  const [sourceFilter, setSourceFilter] = useState('');
  const [dateFilter, setDateFilter] = useState('');
  const [expandedLogs, setExpandedLogs] = useState(new Set());

  const levels = ['error', 'warning', 'info', 'success'];
  const sources = [...new Set(logs.map(log => log.source).filter(Boolean))];

  const filteredLogs = logs.filter(log => {
    if (searchTerm && !log.message.toLowerCase().includes(searchTerm.toLowerCase())) {
      return false;
    }
    if (levelFilter && log.level.toLowerCase() !== levelFilter.toLowerCase()) {
      return false;
    }
    if (sourceFilter && log.source !== sourceFilter) {
      return false;
    }
    if (dateFilter) {
      const logDate = new Date(log.timestamp).toDateString();
      const filterDate = new Date(dateFilter).toDateString();
      if (logDate !== filterDate) {
        return false;
      }
    }
    return true;
  });

  const handleExpandLog = (logId) => {
    const newExpanded = new Set(expandedLogs);
    if (newExpanded.has(logId)) {
      newExpanded.delete(logId);
    } else {
      newExpanded.add(logId);
    }
    setExpandedLogs(newExpanded);
  };

  const handleClearFilters = () => {
    setSearchTerm('');
    setLevelFilter('');
    setSourceFilter('');
    setDateFilter('');
  };

  const getLevelStats = () => {
    const stats = {};
    levels.forEach(level => {
      stats[level] = logs.filter(log => log.level.toLowerCase() === level).length;
    });
    return stats;
  };

  const levelStats = getLevelStats();

  return (
    <div className="space-y-6">
      {/* Controls */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Системные логи</h3>
          <div className="flex items-center gap-3">
            <button
              onClick={onRefresh}
              disabled={isLoading}
              className="flex items-center gap-2 px-3 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
            >
              <FiRefreshCw className={isLoading ? 'animate-spin' : ''} size={16} />
              Обновить
            </button>
            <button
              onClick={onExport}
              className="flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              <FiDownload size={16} />
              Экспорт
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <div className="relative">
            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Поиск в логах..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <select
            value={levelFilter}
            onChange={(e) => setLevelFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Все уровни</option>
            {levels.map(level => (
              <option key={level} value={level}>
                {level.charAt(0).toUpperCase() + level.slice(1)} ({levelStats[level]})
              </option>
            ))}
          </select>

          <select
            value={sourceFilter}
            onChange={(e) => setSourceFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Все источники</option>
            {sources.map(source => (
              <option key={source} value={source}>
                {source}
              </option>
            ))}
          </select>

          <input
            type="date"
            value={dateFilter}
            onChange={(e) => setDateFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />

          <button
            onClick={handleClearFilters}
            className="flex items-center justify-center gap-2 px-3 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
          >
            <FiX size={16} />
            Очистить
          </button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {levels.map(level => (
          <div key={level} className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 capitalize">{level}</p>
                <p className="text-2xl font-bold text-gray-900">{levelStats[level]}</p>
              </div>
              <div className={`p-2 rounded-full ${
                level === 'error' ? 'bg-red-100' :
                level === 'warning' ? 'bg-yellow-100' :
                level === 'info' ? 'bg-blue-100' : 'bg-green-100'
              }`}>
                {level === 'error' && <FiAlertTriangle className="text-red-600" size={20} />}
                {level === 'warning' && <FiAlertTriangle className="text-yellow-600" size={20} />}
                {level === 'info' && <FiInfo className="text-blue-600" size={20} />}
                {level === 'success' && <FiCheckCircle className="text-green-600" size={20} />}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Logs */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-gray-900">
              Записи логов ({filteredLogs.length})
            </h4>
            {filteredLogs.length !== logs.length && (
              <span className="text-sm text-gray-500">
                Показано {filteredLogs.length} из {logs.length}
              </span>
            )}
          </div>
        </div>

        <div className="p-4">
          {isLoading ? (
            <div className="flex items-center justify-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-2 text-gray-600">Загрузка логов...</span>
            </div>
          ) : filteredLogs.length === 0 ? (
            <div className="text-center py-8">
              <FiInfo size={48} className="mx-auto text-gray-300 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Логи не найдены</h3>
              <p className="text-gray-600">
                {logs.length === 0 
                  ? 'Нет записей в логах'
                  : 'Попробуйте изменить фильтры поиска'
                }
              </p>
            </div>
          ) : (
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {filteredLogs.map(log => (
                <LogEntry
                  key={log.id}
                  log={log}
                  onExpand={handleExpandLog}
                  isExpanded={expandedLogs.has(log.id)}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SystemLogs;
