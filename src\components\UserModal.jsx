import React, { useState } from 'react';
import { FiX, FiUser, FiMail, FiCalendar, FiActivity, FiAward, FiBarChart } from 'react-icons/fi';
import { formatDate, formatDateTime } from '../utils/helpers';
import { USER_STATUS } from '../utils/constants';

const UserModal = ({ user, isOpen, onClose, onUpdateStatus }) => {
  const [isUpdating, setIsUpdating] = useState(false);
  const [statusReason, setStatusReason] = useState('');

  if (!isOpen || !user) return null;

  const handleStatusUpdate = async (newStatus) => {
    if (newStatus === USER_STATUS.BLOCKED && !statusReason.trim()) {
      alert('Укажите причину блокировки');
      return;
    }

    setIsUpdating(true);
    try {
      await onUpdateStatus(user.id, newStatus, statusReason);
      onClose();
    } catch (error) {
      console.error('Error updating user status:', error);
    } finally {
      setIsUpdating(false);
      setStatusReason('');
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case USER_STATUS.ACTIVE:
        return 'bg-green-100 text-green-800';
      case USER_STATUS.BLOCKED:
        return 'bg-red-100 text-red-800';
      case USER_STATUS.INACTIVE:
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case USER_STATUS.ACTIVE:
        return 'Активен';
      case USER_STATUS.BLOCKED:
        return 'Заблокирован';
      case USER_STATUS.INACTIVE:
        return 'Неактивен';
      default:
        return 'Неизвестно';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            Информация о пользователе
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <FiX size={24} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* User Info */}
          <div className="flex items-start gap-4 mb-6">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
              {user.image_url ? (
                <img 
                  src={user.image_url} 
                  alt={`${user.name} ${user.surname}`}
                  className="w-16 h-16 rounded-full object-cover"
                />
              ) : (
                <FiUser size={24} className="text-blue-600" />
              )}
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-medium text-gray-900">
                {user.name} {user.surname}
              </h3>
              <p className="text-gray-600 flex items-center gap-2 mt-1">
                <FiMail size={16} />
                {user.email}
              </p>
              <div className="flex items-center gap-2 mt-2">
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(user.status)}`}>
                  {getStatusText(user.status)}
                </span>
                {user.role === 'admin' && (
                  <span className="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    Администратор
                  </span>
                )}
                {!user.activated && (
                  <span className="px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                    Не активирован
                  </span>
                )}
              </div>
            </div>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-gray-50 p-4 rounded-lg text-center">
              <FiCalendar className="mx-auto mb-2 text-gray-600" size={20} />
              <div className="text-sm text-gray-600">Регистрация</div>
              <div className="font-medium">{formatDate(user.created_at)}</div>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg text-center">
              <FiActivity className="mx-auto mb-2 text-gray-600" size={20} />
              <div className="text-sm text-gray-600">Последняя активность</div>
              <div className="font-medium">{user.last_activity ? formatDate(user.last_activity) : 'Никогда'}</div>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg text-center">
              <FiBarChart className="mx-auto mb-2 text-gray-600" size={20} />
              <div className="text-sm text-gray-600">Модули</div>
              <div className="font-medium">{user.completed_modules || 0}</div>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg text-center">
              <FiAward className="mx-auto mb-2 text-gray-600" size={20} />
              <div className="text-sm text-gray-600">Время обучения</div>
              <div className="font-medium">{user.total_time_spent || '00:00:00'}</div>
            </div>
          </div>

          {/* Additional Stats */}
          <div className="grid grid-cols-2 gap-4 mb-6">
            <div className="bg-gray-50 p-4 rounded-lg text-center">
              <div className="text-sm text-gray-600">Текущая серия</div>
              <div className="font-medium text-lg">{user.current_streak || 0} дней</div>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg text-center">
              <div className="text-sm text-gray-600">Прогресс</div>
              <div className="font-medium text-lg">{user.progress || 0}%</div>
            </div>
          </div>

          {/* Status Management */}
          <div className="border-t border-gray-200 pt-6">
            <h4 className="text-lg font-medium text-gray-900 mb-4">
              Управление статусом
            </h4>
            
            {user.status === USER_STATUS.BLOCKED && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
                <p className="text-sm text-red-800">
                  <strong>Причина блокировки:</strong> {user.block_reason || 'Не указана'}
                </p>
              </div>
            )}

            {user.status === USER_STATUS.ACTIVE ? (
              <div>
                <div className="mb-3">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Причина блокировки
                  </label>
                  <textarea
                    value={statusReason}
                    onChange={(e) => setStatusReason(e.target.value)}
                    placeholder="Укажите причину блокировки пользователя..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                    rows={3}
                  />
                </div>
                <button
                  onClick={() => handleStatusUpdate(USER_STATUS.BLOCKED)}
                  disabled={isUpdating}
                  className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isUpdating ? 'Блокировка...' : 'Заблокировать пользователя'}
                </button>
              </div>
            ) : user.status === USER_STATUS.BLOCKED ? (
              <button
                onClick={() => handleStatusUpdate(USER_STATUS.ACTIVE)}
                disabled={isUpdating}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isUpdating ? 'Разблокировка...' : 'Разблокировать пользователя'}
              </button>
            ) : (
              <div className="flex gap-2">
                <button
                  onClick={() => handleStatusUpdate(USER_STATUS.ACTIVE)}
                  disabled={isUpdating}
                  className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isUpdating ? 'Активация...' : 'Активировать пользователя'}
                </button>
                <div className="flex-1">
                  <div className="mb-3">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Причина блокировки
                    </label>
                    <textarea
                      value={statusReason}
                      onChange={(e) => setStatusReason(e.target.value)}
                      placeholder="Укажите причину блокировки пользователя..."
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                      rows={2}
                    />
                  </div>
                  <button
                    onClick={() => handleStatusUpdate(USER_STATUS.BLOCKED)}
                    disabled={isUpdating}
                    className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isUpdating ? 'Блокировка...' : 'Заблокировать пользователя'}
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end gap-3 p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
          >
            Закрыть
          </button>
        </div>
      </div>
    </div>
  );
};

export default UserModal;
