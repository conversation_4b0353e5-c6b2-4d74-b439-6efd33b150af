import axios from 'axios';
import toast from 'react-hot-toast';

// API Configuration
const API_BASE_URL = 'http://localhost:8080/v1';

// Create axios instance
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Flag to prevent multiple refresh attempts
let isRefreshing = false;
let failedQueue = [];

const processQueue = (error, token = null) => {
  failedQueue.forEach(prom => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });

  failedQueue = [];
};

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('accessToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Don't override Content-Type for FormData
    if (config.data instanceof FormData) {
      console.log('🎵 FormData detected, removing Content-Type header');
      delete config.headers['Content-Type'];
    }

    console.log('🎵 Request config:', {
      url: config.url,
      method: config.method,
      headers: config.headers,
      dataType: config.data?.constructor?.name
    });

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling and token refresh
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      if (isRefreshing) {
        // If already refreshing, queue the request
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        }).then(token => {
          originalRequest.headers.Authorization = `Bearer ${token}`;
          return apiClient(originalRequest);
        }).catch(err => {
          return Promise.reject(err);
        });
      }

      originalRequest._retry = true;
      isRefreshing = true;

      const refreshToken = localStorage.getItem('refreshToken');

      if (refreshToken) {
        try {
          // Try to refresh the token
          const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {
            refresh_token: refreshToken
          });

          // API returns data in 'response' field
          const responseData = response.data.response || response.data;
          const { tokens } = responseData;
          localStorage.setItem('accessToken', tokens.access_token);
          localStorage.setItem('refreshToken', tokens.refresh_token);

          // Update the authorization header
          apiClient.defaults.headers.common['Authorization'] = `Bearer ${tokens.access_token}`;
          originalRequest.headers.Authorization = `Bearer ${tokens.access_token}`;

          processQueue(null, tokens.access_token);

          return apiClient(originalRequest);
        } catch (refreshError) {
          processQueue(refreshError, null);

          // Refresh failed, logout user
          localStorage.removeItem('accessToken');
          localStorage.removeItem('refreshToken');
          localStorage.removeItem('user');
          window.location.href = '/login';
          toast.error('Сессия истекла. Пожалуйста, войдите снова.');

          return Promise.reject(refreshError);
        } finally {
          isRefreshing = false;
        }
      } else {
        // No refresh token, logout user
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');
        localStorage.removeItem('user');
        window.location.href = '/login';
        toast.error('Сессия истекла. Пожалуйста, войдите снова.');
      }
    }

    // Handle other error statuses
    if (error.response) {
      const { status, data } = error.response;

      switch (status) {
        case 403:
          toast.error('Недостаточно прав доступа');
          break;
        case 404:
          toast.error('Ресурс не найден');
          break;
        case 422:
          toast.error('Ошибка валидации данных');
          break;
        case 429:
          toast.error('Превышен лимит запросов');
          break;
        case 500:
          toast.error('Внутренняя ошибка сервера');
          break;
        default:
          if (status !== 401) { // Don't show error for 401 as it's handled above
            toast.error(data?.error || 'Произошла ошибка');
          }
      }
    } else if (error.request) {
      toast.error('Ошибка сети. Проверьте подключение к интернету.');
    } else {
      toast.error('Произошла неизвестная ошибка');
    }

    return Promise.reject(error);
  }
);

export default apiClient;
