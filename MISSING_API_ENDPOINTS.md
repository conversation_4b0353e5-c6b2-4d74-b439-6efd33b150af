# Недостающие API Endpoints для полной интеграции

## 🚨 КРИТИЧЕСКИ ВАЖНЫЕ ENDPOINTS

### 1. Refresh Token (ОБЯЗАТЕЛЬНО)

#### POST /v1/auth/refresh
Обновление access token с помощью refresh token

**Параметры запроса:**
```json
{
  "refresh_token": "string (required)"
}
```

**Ответ (200 OK):**
```json
{
  "tokens": {
    "access_token": "new_jwt_access_token",
    "refresh_token": "new_jwt_refresh_token"
  }
}
```

**Ошибки:**
- `401` - Недействительный refresh token
- `403` - Refresh token истек

---

### 2. Административное управление пользователями

#### GET /v1/admin/users
Получение списка пользователей с фильтрами и пагинацией

**Query параметры:**
- `page` (optional) - Номер страницы (default: 1)
- `limit` (optional) - Количество записей на странице (default: 20)
- `search` (optional) - Поиск по имени/email
- `status` (optional) - Фильтр по статусу (active|inactive|banned)
- `sortBy` (optional) - Поле для сортировки (created_at|name|email)
- `sortOrder` (optional) - Порядок сортировки (asc|desc)

**Ответ (200 OK):**
```json
{
  "users": [
    {
      "id": 1,
      "name": "Айдар",
      "surname": "Нурланов",
      "email": "<EMAIL>",
      "status": "active",
      "created_at": "2024-01-01T12:00:00Z",
      "last_login": "2024-01-15T10:30:00Z",
      "modules_completed": 5,
      "total_time_spent": "02:45:30",
      "image_url": ""
    }
  ],
  "total": 1250,
  "page": 1,
  "limit": 20,
  "totalPages": 63
}
```

#### PUT /v1/admin/users/{id}/status
Изменение статуса пользователя

**Параметры запроса:**
```json
{
  "status": "active|inactive|banned",
  "reason": "string (optional)"
}
```

**Ответ (200 OK):**
```json
{
  "message": "User status updated successfully",
  "user": {
    "id": 1,
    "status": "banned",
    "reason": "Violation of terms"
  }
}
```

#### DELETE /v1/admin/users/{id}
Удаление пользователя

**Ответ (200 OK):**
```json
{
  "message": "User deleted successfully"
}
```

---

### 3. Статистика для Dashboard

#### GET /v1/admin/stats/overview
Общая статистика системы

**Ответ (200 OK):**
```json
{
  "total_users": 1250,
  "active_users_today": 89,
  "active_users_week": 456,
  "total_modules": 15,
  "total_questions": 450,
  "total_words": 2300,
  "total_sentences": 1890,
  "total_theories": 89,
  "avg_completion_rate": 67.5,
  "popular_modules": [
    {
      "id": 1,
      "name": "Базовые приветствия",
      "users_count": 234,
      "completion_rate": 85.2
    }
  ],
  "recent_activity": [
    {
      "user_id": 1,
      "user_name": "Айдар Нурланов",
      "action": "completed_module",
      "module_name": "Базовые приветствия",
      "timestamp": "2024-01-15T10:30:00Z"
    }
  ]
}
```

#### GET /v1/admin/stats/users
Статистика пользователей

**Query параметры:**
- `range` (optional) - Временной диапазон (7d|30d|90d|1y)

**Ответ (200 OK):**
```json
{
  "total_users": 1250,
  "new_users_today": 12,
  "new_users_week": 89,
  "active_users": 456,
  "user_growth": [
    { "date": "2024-01-01", "new_users": 15, "active_users": 120 },
    { "date": "2024-01-02", "new_users": 18, "active_users": 135 }
  ],
  "user_retention": {
    "day_1": 85.2,
    "day_7": 67.8,
    "day_30": 45.3
  }
}
```

#### GET /v1/admin/stats/content
Статистика контента

**Query параметры:**
- `range` (optional) - Временной диапазон (7d|30d|90d|1y)

**Ответ (200 OK):**
```json
{
  "totalWords": 2456,
  "totalSentences": 1890,
  "totalQuestions": 567,
  "totalTheories": 89,
  "totalModules": 23,
  "contentGrowth": {
    "words": 15.2,
    "sentences": 12.8,
    "questions": 8.5,
    "theories": 22.1,
    "modules": 5.3
  },
  "contentUsage": [
    { "name": "Базовые приветствия", "views": 1250, "interactions": 890 },
    { "name": "Семья и родственники", "views": 980, "interactions": 720 }
  ]
}
```

---

## 🔧 ДОПОЛНИТЕЛЬНЫЕ CRUD ОПЕРАЦИИ

### Слова

#### PUT /v1/word/with-audio/{id}
Обновление слова с поддержкой аудио

**Content-Type:** `multipart/form-data`

**Параметры запроса:**
- `kaz_plaintext` (string, optional) - Текст на казахском
- `rus_plaintext` (string, optional) - Текст на русском
- `audio` (file, optional) - Аудио файл (MP3, WAV, OGG, M4A, до 10MB)

#### DELETE /v1/word/{id}
Удаление слова

### Предложения

#### PUT /v1/sentence/with-audio/{id}
Обновление предложения с поддержкой аудио

**Content-Type:** `multipart/form-data`

**Параметры запроса:**
- `kaz_plaintext` (string, optional) - Текст на казахском
- `rus_plaintext` (string, optional) - Текст на русском
- `audio` (file, optional) - Аудио файл (MP3, WAV, OGG, M4A, до 10MB)

#### DELETE /v1/sentence/{id}
Удаление предложения

### Вопросы

#### PUT /v1/questions/{id}
Обновление вопроса

**Параметры запроса:**
```json
{
  "type": "string (optional)",
  "words": "array (optional)",
  "correct_answer": "string (optional)",
  "image_url": "string (optional)"
}
```

#### DELETE /v1/questions/{id}
Удаление вопроса

---

## 🖥️ СИСТЕМНОЕ АДМИНИСТРИРОВАНИЕ

### GET /v1/admin/system/settings
Получение системных настроек

**Ответ (200 OK):**
```json
{
  "general": {
    "siteName": "Klingo Admin",
    "siteDescription": "Административная панель для изучения казахского языка",
    "defaultLanguage": "ru",
    "timezone": "Asia/Almaty",
    "maintenanceMode": false
  },
  "email": {
    "smtpHost": "smtp.gmail.com",
    "smtpPort": 587,
    "smtpUser": "<EMAIL>",
    "fromEmail": "<EMAIL>",
    "fromName": "Klingo"
  },
  "security": {
    "sessionTimeout": 60,
    "maxLoginAttempts": 5,
    "passwordMinLength": 8,
    "requireTwoFactor": false,
    "allowRegistration": true
  },
  "storage": {
    "maxFileSize": 10,
    "allowedFileTypes": "jpg,jpeg,png,gif,mp3,wav,pdf",
    "autoCleanup": true,
    "cleanupDays": 30
  }
}
```

### PUT /v1/admin/system/settings
Обновление системных настроек

**Параметры запроса:** (такие же как в ответе GET)

### GET /v1/admin/system/monitor
Мониторинг системы

**Ответ (200 OK):**
```json
{
  "cpu_usage": 45.2,
  "memory_usage": 67.8,
  "disk_usage": 34.5,
  "active_connections": 89,
  "uptime": 2592000,
  "services": [
    {
      "name": "Web Server",
      "description": "Nginx HTTP Server",
      "status": "running",
      "responseTime": 25
    },
    {
      "name": "Database",
      "description": "PostgreSQL Database",
      "status": "running",
      "responseTime": 12
    }
  ]
}
```

### GET /v1/admin/system/logs
Получение системных логов

**Query параметры:**
- `level` (optional) - Уровень логов (error|warn|info|debug)
- `limit` (optional) - Количество записей (default: 100)
- `offset` (optional) - Смещение для пагинации

**Ответ (200 OK):**
```json
{
  "logs": [
    {
      "timestamp": "2024-01-15T10:30:00Z",
      "level": "info",
      "message": "User login successful",
      "user_id": 123,
      "ip_address": "***********"
    }
  ],
  "total": 1500,
  "limit": 100,
  "offset": 0
}
```

---

## 📊 АНАЛИТИКА

### GET /v1/admin/analytics/{type}
Получение аналитических данных

**Path параметры:**
- `type` - Тип аналитики (users|content|system|performance)

**Query параметры:**
- `range` - Временной диапазон (7d|30d|90d|1y)

### POST /v1/admin/analytics/export
Экспорт аналитических данных

**Параметры запроса:**
```json
{
  "type": "users|content|system",
  "dateRange": "30d",
  "format": "csv|json|xlsx"
}
```

---

## 🔐 БЕЗОПАСНОСТЬ И ПРАВА ДОСТУПА

Все административные endpoints (`/v1/admin/*`) должны:

1. Требовать аутентификации (Bearer token)
2. Проверять административные права пользователя
3. Логировать все действия
4. Поддерживать rate limiting
5. Возвращать консистентные ошибки

## 📝 ПРИМЕЧАНИЯ ПО РЕАЛИЗАЦИИ

1. **Пагинация**: Все списочные endpoints должны поддерживать пагинацию
2. **Фильтрация**: Добавить поддержку фильтров для удобства администрирования
3. **Сортировка**: Поддержка сортировки по различным полям
4. **Валидация**: Строгая валидация всех входных данных
5. **Логирование**: Детальное логирование всех административных действий
6. **Кэширование**: Кэширование статистических данных для производительности
