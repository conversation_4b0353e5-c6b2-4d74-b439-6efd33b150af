import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, FiEdit2, <PERSON>Trash2, <PERSON><PERSON><PERSON>, <PERSON>Search, <PERSON>Loader, FiTag, FiFileText, FiX } from 'react-icons/fi';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import TheoryModal from '../components/TheoryModal';
import Table from '../components/Table';
import { mockDataService } from '../services/mockData';
import contentService from '../services/content';
import { debounce, truncateText, formatDate } from '../utils/helpers';

// Use mock service in development mode
const USE_MOCK = false;

// Mock theories data
const mockTheories = [
  {
    id: 1,
    name: 'Базовые приветствия',
    content: '<h2>Приветствие в казахском языке</h2><p>В казахском языке существует несколько способов поприветствовать собеседника...</p>',
    tags: ['приветствие', 'базовый', 'общение'],
    sentence_ids: [1, 2],
    created_at: '2024-01-01T12:00:00Z'
  },
  {
    id: 2,
    name: 'Семья и родственники',
    content: '<h2>Семейные отношения</h2><p>Семья играет важную роль в казахской культуре. Рассмотрим основные термины...</p>',
    tags: ['семья', 'родственники', 'культура'],
    sentence_ids: [3],
    created_at: '2024-01-02T12:00:00Z'
  },
  {
    id: 3,
    name: 'Еда и напитки',
    content: '<h2>Казахская кухня</h2><p>Традиционная казахская кухня богата и разнообразна...</p>',
    tags: ['еда', 'кухня', 'традиции'],
    sentence_ids: [],
    created_at: '2024-01-03T12:00:00Z'
  },
];

const Theories = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTheory, setSelectedTheory] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [previewTheory, setPreviewTheory] = useState(null);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);

  const queryClient = useQueryClient();

  // Fetch theories
  const { data: theories = [], isLoading, error, refetch } = useQuery({
    queryKey: ['theories', searchTerm],
    queryFn: async () => {
      if (USE_MOCK) {
        let filteredTheories = [...mockTheories];
        if (searchTerm) {
          filteredTheories = filteredTheories.filter(theory =>
            (theory.title && theory.title.toLowerCase().includes(searchTerm.toLowerCase())) ||
            (theory.description && theory.description.toLowerCase().includes(searchTerm.toLowerCase())) ||
            (theory.tags && theory.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase())))
          );
        }
        return filteredTheories;
      } else {
        return await contentService.theories.getAll();
      }
    },
    staleTime: 30 * 1000,
  });

  // Fetch sentences for modal
  const { data: sentences = [] } = useQuery({
    queryKey: ['sentences'],
    queryFn: async () => {
      if (USE_MOCK) {
        return [
          { id: 1, kaz_plaintext: 'Менің атым Айдар.', rus_plaintext: 'Меня зовут Айдар.' },
          { id: 2, kaz_plaintext: 'Сіз қалай жақсысыз?', rus_plaintext: 'Как дела?' },
          { id: 3, kaz_plaintext: 'Менің анам үйде.', rus_plaintext: 'Моя мама дома.' },
        ];
      } else {
        return await contentService.sentences.getAll();
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Create theory mutation
  const createTheoryMutation = useMutation({
    mutationFn: async (theoryData) => {
      if (USE_MOCK) {
        await new Promise(resolve => setTimeout(resolve, 1000));
        return { id: Date.now(), ...theoryData, created_at: new Date().toISOString() };
      } else {
        return await contentService.theories.create(theoryData);
      }
    },
    onSuccess: () => {
      toast.success('Теория успешно создана');
      queryClient.invalidateQueries(['theories']);
      setIsModalOpen(false);
      setSelectedTheory(null);
    },
    onError: (error) => {
      toast.error('Ошибка при создании теории');
      console.error('Create theory error:', error);
    }
  });

  // Update theory mutation
  const updateTheoryMutation = useMutation({
    mutationFn: async ({ id, theoryData }) => {
      if (USE_MOCK) {
        await new Promise(resolve => setTimeout(resolve, 1000));
        return { id, ...theoryData };
      } else {
        return await contentService.theories.update(id, theoryData);
      }
    },
    onSuccess: () => {
      toast.success('Теория успешно обновлена');
      queryClient.invalidateQueries(['theories']);
      setIsModalOpen(false);
      setSelectedTheory(null);
    },
    onError: (error) => {
      toast.error('Ошибка при обновлении теории');
      console.error('Update theory error:', error);
    }
  });

  // Delete theory mutation
  const deleteTheoryMutation = useMutation({
    mutationFn: async (theoryId) => {
      if (USE_MOCK) {
        await new Promise(resolve => setTimeout(resolve, 500));
        return { success: true };
      } else {
        return await contentService.theories.delete(theoryId);
      }
    },
    onSuccess: () => {
      toast.success('Теория успешно удалена');
      queryClient.invalidateQueries(['theories']);
    },
    onError: (error) => {
      toast.error('Ошибка при удалении теории');
      console.error('Delete theory error:', error);
    }
  });

  // Debounced search
  const debouncedSearch = debounce((value) => {
    setSearchTerm(value);
  }, 300);

  const handleSearch = (e) => {
    debouncedSearch(e.target.value);
  };

  const handleCreateTheory = () => {
    setSelectedTheory(null);
    setIsModalOpen(true);
  };

  const handleEditTheory = (theory) => {
    setSelectedTheory(theory);
    setIsModalOpen(true);
  };

  const handleDeleteTheory = (theory) => {
    const theoryName = theory.title || `теорию #${theory.id}`;
    if (window.confirm(`Вы уверены, что хотите удалить "${theoryName}"?`)) {
      deleteTheoryMutation.mutate(theory.id);
    }
  };

  const handlePreviewTheory = (theory) => {
    setPreviewTheory(theory);
    setIsPreviewOpen(true);
  };

  const handleSaveTheory = (theoryData) => {
    if (selectedTheory) {
      updateTheoryMutation.mutate({ id: selectedTheory.id, theoryData });
    } else {
      createTheoryMutation.mutate(theoryData);
    }
  };



  // Table columns
  const columns = [
    {
      header: 'Название',
      accessor: 'title',
      render: (row) => (
        <div>
          <div className="font-medium text-gray-900">{row.title || 'Без названия'}</div>
          <div className="text-sm text-gray-500">{formatDate(row.created_at)}</div>
        </div>
      )
    },
    {
      header: 'Описание',
      accessor: 'description',
      render: (row) => (
        <div className="max-w-md">
          <div className="text-sm text-gray-700 leading-relaxed">
            {row.description ? truncateText(row.description, 200) : 'Нет описания'}
          </div>
        </div>
      )
    },
    {
      header: 'Теги',
      accessor: 'tags',
      render: (row) => (
        <div className="flex flex-wrap gap-1">
          {row.tags?.slice(0, 3).map((tag, index) => (
            <span 
              key={index}
              className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
            >
              <FiTag size={10} />
              {tag}
            </span>
          ))}
          {row.tags?.length > 3 && (
            <span className="text-xs text-gray-500">+{row.tags.length - 3}</span>
          )}
        </div>
      )
    },
    {
      header: 'Примеры',
      accessor: 'examples',
      render: (row) => (
        <div className="max-w-sm">
          {row.examples && row.examples.length > 0 ? (
            <div className="space-y-2">
              {row.examples.slice(0, 2).map((example, index) => (
                <div key={example.id || index} className="text-xs bg-gray-50 p-2 rounded">
                  <div className="font-medium text-gray-900 mb-1">
                    {truncateText(example.kaz_plaintext, 50)}
                  </div>
                  <div className="text-gray-500">
                    {truncateText(example.rus_plaintext, 50)}
                  </div>
                </div>
              ))}
              {row.examples.length > 2 && (
                <div className="text-xs text-blue-600 text-center">
                  +{row.examples.length - 2} еще...
                </div>
              )}
            </div>
          ) : (
            <span className="text-gray-400 text-sm">Нет примеров</span>
          )}
        </div>
      )
    },
    {
      header: 'Модуль',
      accessor: 'module_id',
      render: (row) => (
        <div className="text-sm text-gray-600">
          {row.module_id ? `Модуль #${row.module_id}` : 'Не указан'}
        </div>
      )
    },
    {
      header: 'Действия',
      accessor: 'actions',
      render: (row) => (
        <div className="flex items-center gap-2">
          <button
            onClick={() => handlePreviewTheory(row)}
            className="p-1 text-green-600 hover:text-green-800 hover:bg-green-50 rounded transition-colors"
            title="Предварительный просмотр"
          >
            <FiEye size={16} />
          </button>
          <button
            onClick={() => handleEditTheory(row)}
            className="p-1 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded transition-colors"
            title="Редактировать"
          >
            <FiEdit2 size={16} />
          </button>
          <button
            onClick={() => handleDeleteTheory(row)}
            className="p-1 text-red-600 hover:text-red-800 hover:bg-red-50 rounded transition-colors"
            title="Удалить"
          >
            <FiTrash2 size={16} />
          </button>
        </div>
      )
    }
  ];

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-red-600 mb-2">Ошибка загрузки теорий</div>
          <button 
            onClick={() => refetch()} 
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Попробовать снова
          </button>
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Header */}
      <div className="mb-6">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Управление теориями</h1>
            <p className="text-gray-600 mt-1">
              Создавайте и редактируйте теоретические материалы для обучения
            </p>
          </div>
          <div className="flex items-center gap-3">
            <button
              onClick={handleCreateTheory}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              <FiPlus size={16} />
              Создать теорию
            </button>
          </div>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mt-6">
          <div className="bg-white p-4 rounded-lg border border-gray-200 hover:shadow-md transition-shadow">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <FiFileText className="h-5 w-5 text-blue-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Всего теорий</p>
                <p className="text-2xl font-semibold text-gray-900">{theories.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border border-gray-200 hover:shadow-md transition-shadow">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <FiTag className="h-5 w-5 text-green-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">С тегами</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {theories.filter(t => t.tags && t.tags.length > 0).length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border border-gray-200 hover:shadow-md transition-shadow">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <FiEye className="h-5 w-5 text-purple-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">С примерами</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {theories.filter(t => t.examples && t.examples.length > 0).length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border border-gray-200 hover:shadow-md transition-shadow">
            <div className="flex items-center">
              <div className="p-2 bg-orange-100 rounded-lg">
                <FiSearch className="h-5 w-5 text-orange-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Всего примеров</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {theories.reduce((total, theory) => total + (theory.examples?.length || 0), 0)}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Search */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 mb-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Поиск теорий
            </label>
            <div className="relative">
              <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Поиск по названию, описанию или тегам..."
                onChange={handleSearch}
                className="w-full pl-10 pr-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Theories Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <FiLoader className="animate-spin h-8 w-8 text-blue-600" />
            <span className="ml-2 text-gray-600">Загрузка теорий...</span>
          </div>
        ) : theories.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-64 text-gray-500">
            <FiFileText size={48} className="mb-4 text-gray-300" />
            <h3 className="text-lg font-medium mb-2">Теории не найдены</h3>
            <p className="text-sm">Попробуйте изменить параметры поиска или создать новую теорию</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table
              columns={columns}
              data={theories}
            />
          </div>
        )}
      </div>

      {/* Theory Modal */}
      <TheoryModal
        theory={selectedTheory}
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setSelectedTheory(null);
        }}
        onSave={handleSaveTheory}
        isLoading={createTheoryMutation.isLoading || updateTheoryMutation.isLoading}
        sentences={sentences}
      />

      {/* Preview Modal */}
      {isPreviewOpen && previewTheory && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">
                {previewTheory.title || 'Без названия'}
              </h2>
              <button
                onClick={() => setIsPreviewOpen(false)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <FiX size={24} />
              </button>
            </div>
            <div className="p-6">
              {/* Tags */}
              {previewTheory.tags && previewTheory.tags.length > 0 && (
                <div className="mb-4">
                  <div className="flex flex-wrap gap-2">
                    {previewTheory.tags.map((tag, index) => (
                      <span 
                        key={index}
                        className="inline-flex items-center gap-1 px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full"
                      >
                        <FiTag size={12} />
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}
              
              {/* Description */}
              <div className="prose prose-lg max-w-none">
                <p className="text-gray-700 leading-relaxed">
                  {previewTheory.description || 'Описание отсутствует'}
                </p>
              </div>

              {/* Examples */}
              {previewTheory.examples && previewTheory.examples.length > 0 && (
                <div className="mt-6 pt-6 border-t border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900 mb-3">Примеры</h3>
                  <div className="space-y-3">
                    {previewTheory.examples.map(example => (
                      <div key={example.id} className="p-4 bg-gray-50 rounded-lg">
                        <div className="font-medium text-gray-900 mb-1">{example.kaz_plaintext}</div>
                        <div className="text-gray-600">{example.rus_plaintext}</div>
                        {example.audio_url && (
                          <div className="mt-2">
                            <audio controls className="w-full h-8">
                              <source src={example.audio_url} type="audio/mpeg" />
                              Ваш браузер не поддерживает аудио элемент.
                            </audio>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
            <div className="flex justify-end p-6 border-t border-gray-200">
              <button
                onClick={() => setIsPreviewOpen(false)}
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Закрыть
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Theories;
