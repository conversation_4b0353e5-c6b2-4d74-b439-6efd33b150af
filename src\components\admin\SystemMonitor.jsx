import React, { useState, useEffect } from 'react';
import { Line, Doughnut } from 'react-chartjs-2';
import {
  FiServer,
  FiCpu,
  FiHardDrive,
  FiDatabase,
  FiActivity,
  FiAlertTriangle,
  FiCheckCircle,
  FiRefreshCw,
  FiWifi,
  FiUsers
} from 'react-icons/fi';
import { formatFileSize, formatNumber } from '../../utils/helpers';

const MetricCard = ({ title, value, unit, status, icon: Icon, trend, color = 'blue' }) => {
  const getStatusColor = () => {
    switch (status) {
      case 'healthy': return 'text-green-600';
      case 'warning': return 'text-yellow-600';
      case 'critical': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'healthy': return <FiCheckCircle className="text-green-600" size={16} />;
      case 'warning': return <FiAlertTriangle className="text-yellow-600" size={16} />;
      case 'critical': return <FiAlertTriangle className="text-red-600" size={16} />;
      default: return <FiActivity className="text-gray-600" size={16} />;
    }
  };

  const getTrendIcon = () => {
    if (trend > 0) return '↗️';
    if (trend < 0) return '↘️';
    return '➡️';
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
      <div className="flex items-center justify-between mb-4">
        <div className={`p-3 rounded-full bg-${color}-100`}>
          <Icon className={`text-${color}-600`} size={24} />
        </div>
        {getStatusIcon()}
      </div>
      <div>
        <p className="text-sm font-medium text-gray-600">{title}</p>
        <p className={`text-2xl font-bold ${getStatusColor()}`}>
          {unit === 'bytes' ? formatFileSize(value) : formatNumber(value)}
          {unit && unit !== 'bytes' && <span className="text-sm ml-1">{unit}</span>}
        </p>
        {trend !== undefined && (
          <p className="text-xs text-gray-500 mt-1">
            {getTrendIcon()} {Math.abs(trend)}% за час
          </p>
        )}
      </div>
    </div>
  );
};

const SystemMonitor = ({ data, onRefresh, isLoading }) => {
  const [autoRefresh, setAutoRefresh] = useState(false);
  const [refreshInterval, setRefreshInterval] = useState(30);

  useEffect(() => {
    let interval;
    if (autoRefresh && onRefresh) {
      interval = setInterval(() => {
        onRefresh();
      }, refreshInterval * 1000);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [autoRefresh, refreshInterval, onRefresh]);

  const {
    cpu = {},
    memory = {},
    disk = {},
    network = {},
    database = {},
    activeConnections = 0,
    uptime = 0,
    performance = [],
    services = []
  } = data;

  // Performance chart data
  const performanceChartData = {
    labels: performance.map(item => item.time),
    datasets: [
      {
        label: 'CPU (%)',
        data: performance.map(item => item.cpu),
        borderColor: 'rgb(239, 68, 68)',
        backgroundColor: 'rgba(239, 68, 68, 0.1)',
        tension: 0.4,
      },
      {
        label: 'Memory (%)',
        data: performance.map(item => item.memory),
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.4,
      },
      {
        label: 'Disk I/O (MB/s)',
        data: performance.map(item => item.diskIO),
        borderColor: 'rgb(34, 197, 94)',
        backgroundColor: 'rgba(34, 197, 94, 0.1)',
        tension: 0.4,
      },
    ],
  };

  // Memory usage chart
  const memoryChartData = {
    labels: ['Используется', 'Кэш', 'Свободно'],
    datasets: [
      {
        data: [memory.used || 0, memory.cache || 0, memory.free || 0],
        backgroundColor: [
          'rgba(239, 68, 68, 0.8)',
          'rgba(251, 191, 36, 0.8)',
          'rgba(34, 197, 94, 0.8)',
        ],
      },
    ],
  };

  // Disk usage chart
  const diskChartData = {
    labels: ['Используется', 'Свободно'],
    datasets: [
      {
        data: [disk.used || 0, disk.free || 0],
        backgroundColor: [
          'rgba(168, 85, 247, 0.8)',
          'rgba(34, 197, 94, 0.8)',
        ],
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        max: 100,
      },
    },
  };

  const doughnutOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'bottom',
      },
    },
  };

  const formatUptime = (seconds) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${days}д ${hours}ч ${minutes}м`;
  };

  const getServiceStatus = (service) => {
    if (service.status === 'running') return 'healthy';
    if (service.status === 'degraded') return 'warning';
    return 'critical';
  };

  return (
    <div className="space-y-6">
      {/* Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <button
            onClick={onRefresh}
            disabled={isLoading}
            className="flex items-center gap-2 px-3 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
          >
            <FiRefreshCw className={isLoading ? 'animate-spin' : ''} size={16} />
            Обновить
          </button>
          
          <div className="flex items-center gap-2">
            <input
              type="checkbox"
              id="autoRefresh"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="autoRefresh" className="text-sm text-gray-700">
              Автообновление каждые
            </label>
            <select
              value={refreshInterval}
              onChange={(e) => setRefreshInterval(Number(e.target.value))}
              disabled={!autoRefresh}
              className="px-2 py-1 border border-gray-300 rounded text-sm disabled:opacity-50"
            >
              <option value={10}>10с</option>
              <option value={30}>30с</option>
              <option value={60}>1м</option>
              <option value={300}>5м</option>
            </select>
          </div>
        </div>

        <div className="text-sm text-gray-600">
          Время работы: {formatUptime(uptime)}
        </div>
      </div>

      {/* System Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="CPU Usage"
          value={cpu.usage || 0}
          unit="%"
          status={cpu.usage > 80 ? 'critical' : cpu.usage > 60 ? 'warning' : 'healthy'}
          trend={cpu.trend}
          icon={FiCpu}
          color="red"
        />
        <MetricCard
          title="Memory Usage"
          value={memory.usagePercent || 0}
          unit="%"
          status={memory.usagePercent > 85 ? 'critical' : memory.usagePercent > 70 ? 'warning' : 'healthy'}
          trend={memory.trend}
          icon={FiServer}
          color="blue"
        />
        <MetricCard
          title="Disk Usage"
          value={disk.usagePercent || 0}
          unit="%"
          status={disk.usagePercent > 90 ? 'critical' : disk.usagePercent > 75 ? 'warning' : 'healthy'}
          trend={disk.trend}
          icon={FiHardDrive}
          color="green"
        />
        <MetricCard
          title="Active Connections"
          value={activeConnections}
          status={activeConnections > 1000 ? 'warning' : 'healthy'}
          trend={network.connectionsTrend}
          icon={FiWifi}
          color="purple"
        />
      </div>

      {/* Performance Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* System Performance */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Производительность системы
          </h3>
          <div className="h-64">
            <Line data={performanceChartData} options={chartOptions} />
          </div>
        </div>

        {/* Memory Distribution */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Использование памяти
          </h3>
          <div className="h-64 flex items-center justify-center">
            <div className="w-48 h-48">
              <Doughnut data={memoryChartData} options={doughnutOptions} />
            </div>
          </div>
        </div>
      </div>

      {/* Services Status and Disk Usage */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Services Status */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Статус сервисов
          </h3>
          <div className="space-y-3">
            {services.map((service, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                <div className="flex items-center gap-3">
                  <div className={`w-3 h-3 rounded-full ${
                    service.status === 'running' ? 'bg-green-500' :
                    service.status === 'degraded' ? 'bg-yellow-500' : 'bg-red-500'
                  }`} />
                  <div>
                    <div className="font-medium text-sm text-gray-900">{service.name}</div>
                    <div className="text-xs text-gray-500">{service.description}</div>
                  </div>
                </div>
                <div className="text-right">
                  <div className={`text-sm font-medium ${
                    service.status === 'running' ? 'text-green-600' :
                    service.status === 'degraded' ? 'text-yellow-600' : 'text-red-600'
                  }`}>
                    {service.status === 'running' ? 'Работает' :
                     service.status === 'degraded' ? 'Деградация' : 'Остановлен'}
                  </div>
                  {service.responseTime && (
                    <div className="text-xs text-gray-500">
                      {service.responseTime}ms
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Disk Usage */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Использование диска
          </h3>
          <div className="h-64 flex items-center justify-center">
            <div className="w-48 h-48">
              <Doughnut data={diskChartData} options={doughnutOptions} />
            </div>
          </div>
          <div className="mt-4 text-center">
            <div className="text-sm text-gray-600">
              Использовано: {formatFileSize(disk.used || 0)} из {formatFileSize(disk.total || 0)}
            </div>
          </div>
        </div>
      </div>

      {/* Database and Network Stats */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Database Stats */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            База данных
          </h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
              <div className="flex items-center gap-2">
                <FiDatabase className="text-blue-600" size={16} />
                <span className="text-sm font-medium">Размер БД</span>
              </div>
              <span className="text-sm text-gray-600">{formatFileSize(database.size || 0)}</span>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
              <div className="flex items-center gap-2">
                <FiActivity className="text-green-600" size={16} />
                <span className="text-sm font-medium">Запросов/сек</span>
              </div>
              <span className="text-sm text-gray-600">{formatNumber(database.queriesPerSecond || 0)}</span>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
              <div className="flex items-center gap-2">
                <FiUsers className="text-purple-600" size={16} />
                <span className="text-sm font-medium">Активные соединения</span>
              </div>
              <span className="text-sm text-gray-600">{formatNumber(database.activeConnections || 0)}</span>
            </div>
          </div>
        </div>

        {/* Network Stats */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Сеть
          </h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-500 rounded-full" />
                <span className="text-sm font-medium">Входящий трафик</span>
              </div>
              <span className="text-sm text-gray-600">{formatFileSize(network.inbound || 0)}/с</span>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-blue-500 rounded-full" />
                <span className="text-sm font-medium">Исходящий трафик</span>
              </div>
              <span className="text-sm text-gray-600">{formatFileSize(network.outbound || 0)}/с</span>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
              <div className="flex items-center gap-2">
                <FiWifi className="text-orange-600" size={16} />
                <span className="text-sm font-medium">Активные соединения</span>
              </div>
              <span className="text-sm text-gray-600">{formatNumber(activeConnections)}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SystemMonitor;
