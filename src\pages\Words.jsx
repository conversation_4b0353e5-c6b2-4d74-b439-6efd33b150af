import React, { useState } from 'react';
import { FiPlus, FiEdit2, FiTrash2, FiUpload, FiDownload, FiSearch, FiLoader } from 'react-icons/fi';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import WordModal from '../components/WordModal';
import AudioPlayer from '../components/AudioPlayer';
import Table from '../components/Table';
import { mockDataService } from '../services/mockData';
import contentService from '../services/content';
import { debounce } from '../utils/helpers';

// Use mock service in development mode
const USE_MOCK = false;

const Words = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedWord, setSelectedWord] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const queryClient = useQueryClient();

  // Fetch words
  const { data: words = [], isLoading, error, refetch } = useQuery({
    queryKey: ['words', searchTerm],
    queryFn: async () => {
      if (USE_MOCK) {
        const allWords = await mockDataService.getWords();
        if (searchTerm) {
          return allWords.filter(word => 
            word.kaz_plaintext.toLowerCase().includes(searchTerm.toLowerCase()) ||
            word.rus_plaintext.toLowerCase().includes(searchTerm.toLowerCase())
          );
        }
        return allWords;
      } else {
        return await contentService.words.getAll();
      }
    },
    staleTime: 30 * 1000,
  });

  // Create word mutation
  const createWordMutation = useMutation({
    mutationFn: async (wordData) => {
      if (USE_MOCK) {
        await new Promise(resolve => setTimeout(resolve, 1000));
        return { id: Date.now(), ...wordData };
      } else {
        // Extract audio file from wordData
        const { audio_file, ...data } = wordData;
        console.log('🎵 Creating word with audio file:', audio_file);
        return await contentService.words.create(data, audio_file);
      }
    },
    onSuccess: () => {
      toast.success('Слово успешно добавлено');
      queryClient.invalidateQueries(['words']);
      setIsModalOpen(false);
      setSelectedWord(null);
    },
    onError: (error) => {
      toast.error('Ошибка при добавлении слова');
      console.error('Create word error:', error);
    }
  });

  // Update word mutation
  const updateWordMutation = useMutation({
    mutationFn: async ({ id, wordData }) => {
      if (USE_MOCK) {
        await new Promise(resolve => setTimeout(resolve, 1000));
        return { id, ...wordData };
      } else {
        // Extract audio file from wordData
        const { audio_file, ...data } = wordData;
        console.log('🎵 Updating word with audio file:', audio_file);
        return await contentService.words.update(id, data, audio_file);
      }
    },
    onSuccess: () => {
      toast.success('Слово успешно обновлено');
      queryClient.invalidateQueries(['words']);
      setIsModalOpen(false);
      setSelectedWord(null);
    },
    onError: (error) => {
      toast.error('Ошибка при обновлении слова');
      console.error('Update word error:', error);
    }
  });

  // Delete word mutation
  const deleteWordMutation = useMutation({
    mutationFn: async (wordId) => {
      if (USE_MOCK) {
        await new Promise(resolve => setTimeout(resolve, 500));
        return { success: true };
      } else {
        return await contentService.words.delete(wordId);
      }
    },
    onSuccess: () => {
      toast.success('Слово успешно удалено');
      queryClient.invalidateQueries(['words']);
    },
    onError: (error) => {
      toast.error('Ошибка при удалении слова');
      console.error('Delete word error:', error);
    }
  });

  // Debounced search
  const debouncedSearch = debounce((value) => {
    setSearchTerm(value);
  }, 300);

  const handleSearch = (e) => {
    debouncedSearch(e.target.value);
  };

  const handleCreateWord = () => {
    setSelectedWord(null);
    setIsModalOpen(true);
  };

  const handleEditWord = (word) => {
    setSelectedWord(word);
    setIsModalOpen(true);
  };

  const handleDeleteWord = (word) => {
    if (window.confirm(`Вы уверены, что хотите удалить слово "${word.kaz_plaintext}"?`)) {
      deleteWordMutation.mutate(word.id);
    }
  };

  const handleSaveWord = (wordData) => {
    if (selectedWord) {
      updateWordMutation.mutate({ id: selectedWord.id, wordData });
    } else {
      createWordMutation.mutate(wordData);
    }
  };



  // Table columns
  const columns = [
    {
      header: 'Казахский',
      accessor: 'kaz_plaintext',
      render: (row) => (
        <div className="font-medium text-gray-900">
          {row.kaz_plaintext}
        </div>
      )
    },
    {
      header: 'Русский',
      accessor: 'rus_plaintext',
      render: (row) => (
        <div className="text-gray-700">
          {row.rus_plaintext}
        </div>
      )
    },
    {
      header: 'Аудио',
      accessor: 'audio_url',
      render: (row) => (
        <AudioPlayer
          audioUrl={row.audio_url}
          size="sm"
          onPlay={() => console.log('Playing word:', row.kaz_plaintext)}
        />
      )
    },
    {
      header: 'Действия',
      accessor: 'actions',
      render: (row) => (
        <div className="flex items-center gap-2">
          <button
            onClick={() => handleEditWord(row)}
            className="p-1 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded transition-colors"
            title="Редактировать"
          >
            <FiEdit2 size={16} />
          </button>
          <button
            onClick={() => handleDeleteWord(row)}
            className="p-1 text-red-600 hover:text-red-800 hover:bg-red-50 rounded transition-colors"
            title="Удалить"
          >
            <FiTrash2 size={16} />
          </button>
        </div>
      )
    }
  ];

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-red-600 mb-2">Ошибка загрузки словаря</div>
          <button 
            onClick={() => refetch()} 
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Попробовать снова
          </button>
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Словарь</h1>
          <p className="text-gray-600 mt-1">
            Всего слов: {words.length}
          </p>
        </div>
        <div className="flex items-center gap-3">
          <button
            onClick={handleCreateWord}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            <FiPlus size={16} />
            Добавить слово
          </button>
        </div>
      </div>

      {/* Search and filters */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 mb-6">
        <div className="flex items-center gap-4">
          <div className="flex-1 max-w-md">
            <div className="relative">
              <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Поиск по казахскому или русскому слову..."
                onChange={handleSearch}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
          <div className="flex items-center gap-2">
            <button className="flex items-center gap-2 px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
              <FiUpload size={16} />
              Импорт
            </button>
            <button className="flex items-center gap-2 px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
              <FiDownload size={16} />
              Экспорт
            </button>
          </div>
        </div>
      </div>

      {/* Words Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <FiLoader className="animate-spin h-8 w-8 text-blue-600" />
            <span className="ml-2 text-gray-600">Загрузка словаря...</span>
          </div>
        ) : (
          <Table 
            columns={columns} 
            data={words}
          />
        )}
      </div>

      {/* Word Modal */}
      <WordModal
        word={selectedWord}
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setSelectedWord(null);
        }}
        onSave={handleSaveWord}
        isLoading={createWordMutation.isLoading || updateWordMutation.isLoading}
      />
    </div>
  );
};

export default Words;
