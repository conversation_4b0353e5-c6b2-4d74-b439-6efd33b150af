import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>E<PERSON>2, <PERSON><PERSON><PERSON>2, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tag } from 'react-icons/fi';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import QuestionModal from '../components/QuestionModal';
import Table from '../components/Table';
import { mockDataService } from '../services/mockData';
import contentService from '../services/content';
import { debounce, truncateText } from '../utils/helpers';
import { QUESTION_TYPES } from '../utils/constants';

// Use mock service in development mode
const USE_MOCK = false;

// Mock questions data
const mockQuestions = [
  {
    id: 1,
    type: QUESTION_TYPES.TRANSLATION,
    question_text: 'Как сказать "привет" на казахском языке?',
    correct_answer: 'сәлем',
    options: ['сәлем', 'рахмет', 'кешіріңіз', 'сау болыңыз'],
    word_ids: [1],
    audio_url: null,
    image_url: null
  },
  {
    id: 2,
    type: QUESTION_TYPES.AUDIO,
    question_text: 'Что означает это слово?',
    correct_answer: 'спасибо',
    options: ['спасибо', 'привет', 'извините', 'до свидания'],
    word_ids: [2],
    audio_url: 'http://minio:9000/klingo-audio/rahmet.mp3',
    image_url: null
  },
  {
    id: 3,
    type: QUESTION_TYPES.TRANSLATION,
    question_text: 'Переведите "мама" на казахский язык',
    correct_answer: 'ана',
    options: ['ана', 'әке', 'бала', 'дос'],
    word_ids: [4],
    audio_url: null,
    image_url: null
  },
];

const Questions = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('');
  const [selectedQuestion, setSelectedQuestion] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const queryClient = useQueryClient();

  // Fetch questions
  const { data: questions = [], isLoading, error, refetch } = useQuery({
    queryKey: ['questions', searchTerm, typeFilter],
    queryFn: async () => {
      if (USE_MOCK) {
        let filteredQuestions = [...mockQuestions];
        
        if (searchTerm) {
          filteredQuestions = filteredQuestions.filter(question => {
            const searchLower = searchTerm.toLowerCase();
            // Search in question text (if exists)
            if (question.question_text && question.question_text.toLowerCase().includes(searchLower)) {
              return true;
            }
            // Search in correct answer text (if exists)
            if (question.correct_answer) {
              if (typeof question.correct_answer === 'string' && question.correct_answer.toLowerCase().includes(searchLower)) {
                return true;
              }
              if (question.correct_answer.kaz_plaintext && question.correct_answer.kaz_plaintext.toLowerCase().includes(searchLower)) {
                return true;
              }
              if (question.correct_answer.rus_plaintext && question.correct_answer.rus_plaintext.toLowerCase().includes(searchLower)) {
                return true;
              }
            }
            // Search in words
            if (question.words && Array.isArray(question.words)) {
              return question.words.some(word =>
                (word.kaz_plaintext && word.kaz_plaintext.toLowerCase().includes(searchLower)) ||
                (word.rus_plaintext && word.rus_plaintext.toLowerCase().includes(searchLower))
              );
            }
            return false;
          });
        }
        
        if (typeFilter) {
          filteredQuestions = filteredQuestions.filter(question => question.type === typeFilter);
        }
        
        return filteredQuestions;
      } else {
        // Get all questions from API
        const allQuestions = await contentService.questions.getAll();

        // Apply client-side filtering since API doesn't support it yet
        let filteredQuestions = [...allQuestions];

        if (searchTerm) {
          filteredQuestions = filteredQuestions.filter(question => {
            const searchLower = searchTerm.toLowerCase();
            // Search in correct answer
            if (question.correct_answer) {
              if (question.correct_answer.kaz_plaintext && question.correct_answer.kaz_plaintext.toLowerCase().includes(searchLower)) {
                return true;
              }
              if (question.correct_answer.rus_plaintext && question.correct_answer.rus_plaintext.toLowerCase().includes(searchLower)) {
                return true;
              }
            }
            // Search in words
            if (question.words && Array.isArray(question.words)) {
              return question.words.some(word =>
                (word.kaz_plaintext && word.kaz_plaintext.toLowerCase().includes(searchLower)) ||
                (word.rus_plaintext && word.rus_plaintext.toLowerCase().includes(searchLower))
              );
            }
            return false;
          });
        }

        if (typeFilter) {
          filteredQuestions = filteredQuestions.filter(question => question.type === typeFilter);
        }

        return filteredQuestions;
      }
    },
    staleTime: 30 * 1000,
  });

  // Fetch words for modal
  const { data: words = [] } = useQuery({
    queryKey: ['words'],
    queryFn: async () => {
      if (USE_MOCK) {
        return await mockDataService.getWords();
      } else {
        return await contentService.words.getAll();
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Create question mutation
  const createQuestionMutation = useMutation({
    mutationFn: async (questionData) => {
      if (USE_MOCK) {
        await new Promise(resolve => setTimeout(resolve, 1000));
        return { id: Date.now(), ...questionData };
      } else {
        return await contentService.questions.create(questionData);
      }
    },
    onSuccess: () => {
      toast.success('Вопрос успешно создан');
      queryClient.invalidateQueries(['questions']);
      setIsModalOpen(false);
      setSelectedQuestion(null);
    },
    onError: (error) => {
      toast.error('Ошибка при создании вопроса');
      console.error('Create question error:', error);
    }
  });

  // Update question mutation
  const updateQuestionMutation = useMutation({
    mutationFn: async ({ id, questionData }) => {
      if (USE_MOCK) {
        await new Promise(resolve => setTimeout(resolve, 1000));
        return { id, ...questionData };
      } else {
        return await contentService.questions.update(id, questionData);
      }
    },
    onSuccess: () => {
      toast.success('Вопрос успешно обновлен');
      queryClient.invalidateQueries(['questions']);
      setIsModalOpen(false);
      setSelectedQuestion(null);
    },
    onError: (error) => {
      toast.error('Ошибка при обновлении вопроса');
      console.error('Update question error:', error);
    }
  });

  // Delete question mutation
  const deleteQuestionMutation = useMutation({
    mutationFn: async (questionId) => {
      if (USE_MOCK) {
        await new Promise(resolve => setTimeout(resolve, 500));
        return { success: true };
      } else {
        return await contentService.questions.delete(questionId);
      }
    },
    onSuccess: () => {
      toast.success('Вопрос успешно удален');
      queryClient.invalidateQueries(['questions']);
    },
    onError: (error) => {
      toast.error('Ошибка при удалении вопроса');
      console.error('Delete question error:', error);
    }
  });

  // Debounced search
  const debouncedSearch = debounce((value) => {
    setSearchTerm(value);
  }, 300);

  const handleSearch = (e) => {
    debouncedSearch(e.target.value);
  };

  const handleCreateQuestion = () => {
    setSelectedQuestion(null);
    setIsModalOpen(true);
  };

  const handleViewQuestion = (question) => {
    setSelectedQuestion(question);
    setIsModalOpen(true);
  };

  const handleEditQuestion = (question) => {
    setSelectedQuestion(question);
    setIsModalOpen(true);
  };

  const handleDeleteQuestion = (question) => {
    const questionText = question.correct_answer?.kaz_plaintext ||
                        question.words?.map(w => w.kaz_plaintext).join(', ') ||
                        `вопрос #${question.id}`;
    if (window.confirm(`Вы уверены, что хотите удалить ${questionText}?`)) {
      deleteQuestionMutation.mutate(question.id);
    }
  };

  const handleSaveQuestion = (questionData) => {
    if (selectedQuestion) {
      updateQuestionMutation.mutate({ id: selectedQuestion.id, questionData });
    } else {
      createQuestionMutation.mutate(questionData);
    }
  };

  const getQuestionTypeLabel = (type) => {
    const labels = {
      [QUESTION_TYPES.BUILD_SENTENCE]: 'Построение предложения',
      [QUESTION_TYPES.KZ_WORD_TO_RU_WORD]: 'Перевод слов',
      [QUESTION_TYPES.BY_LETTER]: 'По буквам',
    };
    return labels[type] || type;
  };

  const getQuestionTypeColor = (type) => {
    const colors = {
      [QUESTION_TYPES.BUILD_SENTENCE]: 'bg-orange-100 text-orange-800',
      [QUESTION_TYPES.KZ_WORD_TO_RU_WORD]: 'bg-blue-100 text-blue-800',
      [QUESTION_TYPES.BY_LETTER]: 'bg-green-100 text-green-800',
    };
    return colors[type] || 'bg-gray-100 text-gray-800';
  };

  const getRelatedWords = (wordIds) => {
    if (!wordIds || wordIds.length === 0) return [];
    return wordIds
      .map(id => words.find(word => word.id === id))
      .filter(Boolean);
  };

  // Table columns
  const columns = [
    {
      header: 'ID',
      accessor: 'id',
      className: 'hidden sm:table-cell',
      render: (row) => (
        <div className="text-sm font-mono text-gray-600">
          #{row.id}
        </div>
      )
    },
    {
      header: 'Тип',
      accessor: 'type',
      render: (row) => (
        <div className="max-w-32">
          <span className={`px-2 py-1 rounded-full text-xs font-medium whitespace-nowrap ${getQuestionTypeColor(row.type)}`}>
            {getQuestionTypeLabel(row.type)}
          </span>
        </div>
      )
    },
    {
      header: 'Слова',
      accessor: 'words',
      render: (row) => (
        <div className="max-w-sm">
          {row.words && row.words.length > 0 ? (
            <div className="space-y-1">
              {row.words.slice(0, 3).map((word, index) => (
                <div key={word.id || index} className="flex flex-col">
                  <span className="text-sm font-medium text-gray-900 truncate">
                    {word.kaz_plaintext}
                  </span>
                  <span className="text-xs text-gray-500 truncate">
                    {word.rus_plaintext}
                  </span>
                </div>
              ))}
              {row.words.length > 3 && (
                <div className="text-xs text-blue-600">
                  +{row.words.length - 3} еще...
                </div>
              )}
            </div>
          ) : (
            <span className="text-gray-400 text-sm">Нет слов</span>
          )}
        </div>
      )
    },
    {
      header: 'Правильный ответ',
      accessor: 'correct_answer',
      render: (row) => (
        <div className="max-w-sm">
          {row.correct_answer ? (
            <div className="space-y-1">
              <div className="font-medium text-green-700 text-sm truncate">
                {row.correct_answer.kaz_plaintext}
              </div>
              <div className="text-green-600 text-xs truncate">
                {row.correct_answer.rus_plaintext}
              </div>
              {row.correct_answer.sequence && row.correct_answer.sequence.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-1">
                  {row.correct_answer.sequence.map((id, index) => (
                    <span key={index} className="inline-block px-1.5 py-0.5 bg-green-100 text-green-800 text-xs rounded">
                      {id}
                    </span>
                  ))}
                </div>
              )}
            </div>
          ) : (
            <span className="text-gray-400 text-sm">Не указан</span>
          )}
        </div>
      )
    },
    {
      header: 'Связанные слова',
      accessor: 'word_ids',
      className: 'hidden lg:table-cell',
      render: (row) => {
        const relatedWords = getRelatedWords(row.word_ids);
        return (
          <div className="flex items-center gap-1">
            <FiTag size={14} className="text-gray-400" />
            <span className="text-sm text-gray-600">
              {relatedWords.length > 0 
                ? relatedWords.map(word => word.kaz_plaintext).join(', ')
                : 'Нет слов'
              }
            </span>
          </div>
        );
      }
    },
    {
      header: 'Варианты',
      accessor: 'options',
      className: 'hidden md:table-cell',
      render: (row) => (
        <div className="text-sm text-gray-600">
          {row.options?.length || 0} вариантов
        </div>
      )
    },
    {
      header: 'Действия',
      accessor: 'actions',
      render: (row) => (
        <div className="flex items-center gap-1">
          <button
            onClick={() => handleViewQuestion(row)}
            className="p-1.5 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded transition-colors"
            title="Просмотр вопроса"
          >
            <FiEye size={14} />
          </button>
          <button
            onClick={() => handleEditQuestion(row)}
            className="p-1.5 text-green-600 hover:text-green-800 hover:bg-green-50 rounded transition-colors"
            title="Редактировать вопрос"
          >
            <FiEdit2 size={14} />
          </button>
          <button
            onClick={() => handleDeleteQuestion(row)}
            className="p-1.5 text-red-600 hover:text-red-800 hover:bg-red-50 rounded transition-colors"
            title="Удалить вопрос"
          >
            <FiTrash2 size={14} />
          </button>
        </div>
      )
    }
  ];

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-red-600 mb-2">Ошибка загрузки вопросов</div>
          <button 
            onClick={() => refetch()} 
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Попробовать снова
          </button>
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Header */}
      <div className="mb-6">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Управление вопросами</h1>
            <p className="text-gray-600 mt-1">
              Создавайте и редактируйте вопросы для обучения
            </p>
          </div>
          <div className="flex items-center gap-3">
            <button
              onClick={handleCreateQuestion}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              <FiPlus size={16} />
              Создать вопрос
            </button>
          </div>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mt-6">
          <div className="bg-white p-4 rounded-lg border border-gray-200 hover:shadow-md transition-shadow">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <FiSearch className="h-5 w-5 text-blue-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Всего вопросов</p>
                <p className="text-2xl font-semibold text-gray-900">{questions.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border border-gray-200 hover:shadow-md transition-shadow">
            <div className="flex items-center">
              <div className="p-2 bg-orange-100 rounded-lg">
                <FiEdit2 className="h-5 w-5 text-orange-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Построение предложений</p>
                <div className="flex items-baseline gap-2">
                  <p className="text-2xl font-semibold text-gray-900">
                    {questions.filter(q => q.type === QUESTION_TYPES.BUILD_SENTENCE).length}
                  </p>
                  {questions.length > 0 && (
                    <p className="text-sm text-gray-500">
                      ({Math.round((questions.filter(q => q.type === QUESTION_TYPES.BUILD_SENTENCE).length / questions.length) * 100)}%)
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border border-gray-200 hover:shadow-md transition-shadow">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <FiTag className="h-5 w-5 text-blue-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Перевод слов (KZ→RU)</p>
                <div className="flex items-baseline gap-2">
                  <p className="text-2xl font-semibold text-gray-900">
                    {questions.filter(q => q.type === QUESTION_TYPES.KZ_WORD_TO_RU_WORD).length}
                  </p>
                  {questions.length > 0 && (
                    <p className="text-sm text-gray-500">
                      ({Math.round((questions.filter(q => q.type === QUESTION_TYPES.KZ_WORD_TO_RU_WORD).length / questions.length) * 100)}%)
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border border-gray-200 hover:shadow-md transition-shadow">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <FiFilter className="h-5 w-5 text-green-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Ввод по буквам</p>
                <div className="flex items-baseline gap-2">
                  <p className="text-2xl font-semibold text-gray-900">
                    {questions.filter(q => q.type === QUESTION_TYPES.BY_LETTER).length}
                  </p>
                  {questions.length > 0 && (
                    <p className="text-sm text-gray-500">
                      ({Math.round((questions.filter(q => q.type === QUESTION_TYPES.BY_LETTER).length / questions.length) * 100)}%)
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Search and filters */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 mb-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Поиск вопросов
            </label>
            <div className="relative">
              <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Поиск по казахскому или русскому тексту..."
                onChange={handleSearch}
                className="w-full pl-10 pr-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
              />
            </div>
          </div>
          <div className="sm:w-64">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Тип вопроса
            </label>
            <div className="relative">
              <FiFilter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <select
                value={typeFilter}
                onChange={(e) => setTypeFilter(e.target.value)}
                className="w-full pl-10 pr-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none bg-white transition-colors"
              >
                <option value="">Все типы</option>
                <option value={QUESTION_TYPES.BUILD_SENTENCE}>Построение предложения</option>
                <option value={QUESTION_TYPES.KZ_WORD_TO_RU_WORD}>Перевод слов</option>
                <option value={QUESTION_TYPES.BY_LETTER}>По буквам</option>
              </select>
            </div>
          </div>
        </div>

        {/* Filter summary */}
        {(searchTerm || typeFilter) && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="flex flex-wrap items-center gap-2">
              <span className="text-sm text-gray-500">Активные фильтры:</span>
              {searchTerm && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  Поиск: "{searchTerm}"
                </span>
              )}
              {typeFilter && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Тип: {getQuestionTypeLabel(typeFilter)}
                </span>
              )}
              <button
                onClick={() => {
                  setSearchTerm('');
                  setTypeFilter('');
                }}
                className="text-xs text-blue-600 hover:text-blue-800 ml-2"
              >
                Сбросить все
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Questions Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <FiLoader className="animate-spin h-8 w-8 text-blue-600" />
            <span className="ml-2 text-gray-600">Загрузка вопросов...</span>
          </div>
        ) : questions.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-64 text-gray-500">
            <FiSearch size={48} className="mb-4 text-gray-300" />
            <h3 className="text-lg font-medium mb-2">Вопросы не найдены</h3>
            <p className="text-sm">Попробуйте изменить параметры поиска или создать новый вопрос</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table
              columns={columns}
              data={questions}
            />
          </div>
        )}
      </div>

      {/* Question Modal */}
      <QuestionModal
        question={selectedQuestion}
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setSelectedQuestion(null);
        }}
        onSave={handleSaveQuestion}
        isLoading={createQuestionMutation.isLoading || updateQuestionMutation.isLoading}
        words={words}
      />
    </div>
  );
};

export default Questions;
