import apiClient from './api';
import { mockDataService } from './mockData';
import toast from 'react-hot-toast';

// Use mock service in development mode
const USE_MOCK = false;

export const analyticsService = {
  // Get overview statistics
  async getOverviewStats() {
    try {
      if (USE_MOCK) {
        return await mockDataService.getOverviewStats();
      }

      const response = await apiClient.get('/admin/stats/overview');
      return response.data;
    } catch (error) {
      toast.error('Ошибка загрузки общей статистики');
      throw error;
    }
  },

  // Get user statistics
  async getUserStats(dateRange = '30d') {
    try {
      if (USE_MOCK) {
        return await mockDataService.getAnalytics('users', dateRange);
      }

      const response = await apiClient.get(`/admin/stats/users?range=${dateRange}`);
      return response.data;
    } catch (error) {
      toast.error('Ошибка загрузки статистики пользователей');
      throw error;
    }
  },

  // Get content statistics
  async getContentStats(dateRange = '30d') {
    try {
      if (USE_MOCK) {
        return await mockDataService.getAnalytics('content', dateRange);
      }

      const response = await apiClient.get(`/admin/stats/content?range=${dateRange}`);
      return response.data;
    } catch (error) {
      toast.error('Ошибка загрузки статистики контента');
      throw error;
    }
  },

  // Get system statistics
  async getSystemStats(dateRange = '30d') {
    try {
      if (USE_MOCK) {
        return await mockDataService.getAnalytics('system', dateRange);
      }

      const response = await apiClient.get(`/admin/stats/system?range=${dateRange}`);
      return response.data;
    } catch (error) {
      toast.error('Ошибка загрузки системной статистики');
      throw error;
    }
  },

  // Get module statistics
  async getModuleStats(dateRange = '30d') {
    try {
      if (USE_MOCK) {
        return {
          totalModules: 24,
          completedModules: 18,
          averageCompletionTime: "00:15:30",
          popularModules: [
            { id: 1, name: "Базовые приветствия", completions: 234, avgTime: "00:12:45" },
            { id: 2, name: "Семья и родственники", completions: 189, avgTime: "00:18:20" },
            { id: 3, name: "Еда и напитки", completions: 156, avgTime: "00:14:10" }
          ],
          moduleCompletionTrend: [
            { date: '2024-01-01', completions: 45 },
            { date: '2024-01-02', completions: 52 },
            { date: '2024-01-03', completions: 38 },
            { date: '2024-01-04', completions: 61 },
            { date: '2024-01-05', completions: 47 }
          ]
        };
      }

      const response = await apiClient.get(`/admin/stats/modules?range=${dateRange}`);
      return response.data;
    } catch (error) {
      toast.error('Ошибка загрузки статистики модулей');
      throw error;
    }
  },

  // Get performance statistics
  async getPerformanceStats(dateRange = '30d') {
    try {
      if (USE_MOCK) {
        return {
          averageResponseTime: 245,
          totalRequests: 125000,
          errorRate: 0.8,
          uptime: 99.9,
          responseTimeHistory: [
            { timestamp: '2024-01-01T00:00:00Z', responseTime: 230 },
            { timestamp: '2024-01-01T01:00:00Z', responseTime: 245 },
            { timestamp: '2024-01-01T02:00:00Z', responseTime: 220 },
            { timestamp: '2024-01-01T03:00:00Z', responseTime: 260 },
            { timestamp: '2024-01-01T04:00:00Z', responseTime: 235 }
          ],
          errorsByType: [
            { type: '4xx', count: 450 },
            { type: '5xx', count: 120 },
            { type: 'timeout', count: 80 }
          ]
        };
      }

      const response = await apiClient.get(`/admin/stats/performance?range=${dateRange}`);
      return response.data;
    } catch (error) {
      toast.error('Ошибка загрузки статистики производительности');
      throw error;
    }
  },

  // Get analytics data (generic method)
  async getAnalytics(type, dateRange = '30d') {
    try {
      if (USE_MOCK) {
        return await mockDataService.getAnalytics(type, dateRange);
      }

      const response = await apiClient.get(`/admin/analytics/${type}?range=${dateRange}`);
      return response.data;
    } catch (error) {
      toast.error(`Ошибка загрузки аналитики: ${type}`);
      throw error;
    }
  },

  // Get real-time statistics
  async getRealTimeStats() {
    try {
      if (USE_MOCK) {
        return {
          activeUsers: Math.floor(Math.random() * 50) + 20,
          onlineUsers: Math.floor(Math.random() * 30) + 10,
          currentRequests: Math.floor(Math.random() * 100) + 50,
          systemLoad: Math.random() * 100,
          memoryUsage: Math.random() * 100,
          cpuUsage: Math.random() * 100
        };
      }

      const response = await apiClient.get('/admin/stats/realtime');
      return response.data;
    } catch (error) {
      console.error('Ошибка загрузки real-time статистики:', error);
      throw error;
    }
  },

  // Export analytics data
  async exportAnalytics(type, dateRange, format = 'csv') {
    try {
      if (USE_MOCK) {
        toast.success('Экспорт данных начат (mock)');
        return { message: 'Export started', downloadUrl: '#' };
      }

      const response = await apiClient.post('/admin/analytics/export', {
        type,
        dateRange,
        format
      });
      
      toast.success('Экспорт данных начат');
      return response.data;
    } catch (error) {
      toast.error('Ошибка экспорта данных');
      throw error;
    }
  }
};

export default analyticsService;
