# Использование аудио интеграции в админ панели

## 🎵 Обзор

Админ панель теперь полностью интегрирована с новыми endpoints для загрузки аудио файлов. Все операции создания и обновления слов и предложений используют endpoints с поддержкой аудио.

## 🔧 API Endpoints

### Используемые endpoints:
- ✅ `POST /v1/word/with-audio` - создание слова (с аудио или без)
- ✅ `POST /v1/sentence/with-audio` - создание предложения (с аудио или без)
- ⚠️ `PUT /v1/word/with-audio/{id}` - обновление слова (НУЖНО РЕАЛИЗОВАТЬ)
- ⚠️ `PUT /v1/sentence/with-audio/{id}` - обновление предложения (НУЖНО РЕАЛИЗОВАТЬ)

### Старые endpoints больше НЕ используются:
- ❌ `POST /v1/word` - заменен на `/v1/word/with-audio`
- ❌ `POST /v1/sentence` - заменен на `/v1/sentence/with-audio`

## 💻 Использование в коде

### 1. Создание слова с аудио:

```javascript
import { contentService } from '../services/content';

// С аудио файлом
const wordData = {
  kaz_plaintext: "Сәлем",
  rus_plaintext: "Привет"
};
const audioFile = selectedAudioFile; // File object

const newWord = await contentService.words.create(wordData, audioFile);
```

### 2. Создание слова без аудио:

```javascript
// Без аудио файла (аудио параметр опциональный)
const wordData = {
  kaz_plaintext: "Сәлем",
  rus_plaintext: "Привет"
};

const newWord = await contentService.words.create(wordData);
// или явно передать null
const newWord = await contentService.words.create(wordData, null);
```

### 3. Обновление слова:

```javascript
// Обновление с новым аудио
const updatedWord = await contentService.words.update(wordId, wordData, newAudioFile);

// Обновление без изменения аудио
const updatedWord = await contentService.words.update(wordId, wordData);
```

### 4. Использование компонента AudioUpload:

```jsx
import AudioUpload from '../components/AudioUpload';

function WordModal() {
  const [audioFile, setAudioFile] = useState(null);

  const handleSubmit = async (wordData) => {
    try {
      await contentService.words.create(wordData, audioFile);
      // Успех
    } catch (error) {
      // Обработка ошибки
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      {/* Другие поля */}
      
      <AudioUpload
        onFileSelect={setAudioFile}
        selectedFile={audioFile}
        onFileRemove={() => setAudioFile(null)}
      />
      
      <button type="submit">Создать слово</button>
    </form>
  );
}
```

## 🎯 Валидация аудио файлов

### Автоматическая валидация:
- **Форматы**: MP3, WAV, OGG, M4A
- **Размер**: до 10MB
- **Проверка**: выполняется автоматически в `contentService`

### Ручная валидация:
```javascript
import { validateAudioFile } from '../services/content';

try {
  validateAudioFile(audioFile);
  console.log('Файл валиден');
} catch (error) {
  console.error('Ошибка валидации:', error.message);
}
```

## 📋 Структура запросов

### Создание/обновление с аудио:
```
Content-Type: multipart/form-data

kaz_plaintext: "Сәлем"
rus_plaintext: "Привет"
audio: [binary file data]
```

### Создание/обновление без аудио:
```
Content-Type: multipart/form-data

kaz_plaintext: "Сәлем"
rus_plaintext: "Привет"
```

## 🔄 Структура ответов

### Успешный ответ:
```json
{
  "word": {
    "id": 123,
    "kaz_plaintext": "Сәлем",
    "rus_plaintext": "Привет",
    "audio_url": "http://minio:9000/klingo-audio/word_123.mp3",
    "created_at": "2024-01-01T12:00:00Z"
  }
}
```

### Ответ без аудио:
```json
{
  "word": {
    "id": 123,
    "kaz_plaintext": "Сәлем",
    "rus_plaintext": "Привет",
    "audio_url": "",
    "created_at": "2024-01-01T12:00:00Z"
  }
}
```

## ⚠️ Важные замечания

1. **Все запросы используют multipart/form-data**, даже без аудио файла
2. **Аудио файл всегда опциональный** - можно создавать контент без аудио
3. **Валидация выполняется на клиенте** перед отправкой запроса
4. **Старые endpoints больше не используются** в админ панели
5. **Обработка ошибок** включает специфичные сообщения для аудио файлов

## 🚨 Endpoints для реализации на бэкенде

Для полной функциональности нужно реализовать:

```
PUT /v1/word/with-audio/{id}
PUT /v1/sentence/with-audio/{id}
```

Эти endpoints должны:
- Принимать `multipart/form-data`
- Поддерживать опциональный аудио файл
- Обновлять существующие записи
- Возвращать обновленные данные

## 🎉 Готовые компоненты

- ✅ **AudioUpload** - компонент для загрузки аудио с предпросмотром
- ✅ **contentService** - обновленный сервис с поддержкой аудио
- ✅ **Валидация** - автоматическая проверка аудио файлов
- ✅ **Обработка ошибок** - специфичные сообщения для аудио

Теперь можно интегрировать AudioUpload в модалы создания/редактирования слов и предложений!
