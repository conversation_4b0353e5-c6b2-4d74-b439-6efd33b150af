import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { 
  FiSave, 
  FiRefreshCw, 
  FiSettings, 
  FiMail, 
  FiDatabase, 
  FiShield,
  FiGlobe,
  FiClock,
  FiHardDrive
} from 'react-icons/fi';
import toast from 'react-hot-toast';

// Validation schema
const schema = yup.object({
  general: yup.object({
    siteName: yup.string().required('Название сайта обязательно'),
    siteDescription: yup.string().required('Описание сайта обязательно'),
    defaultLanguage: yup.string().required('Язык по умолчанию обязателен'),
    timezone: yup.string().required('Часовой пояс обязателен'),
    maintenanceMode: yup.boolean()
  }),
  email: yup.object({
    smtpHost: yup.string().required('SMTP хост обязателен'),
    smtpPort: yup.number().required('SMTP порт обязателен'),
    smtpUser: yup.string().required('SMTP пользователь обязателен'),
    smtpPassword: yup.string(),
    fromEmail: yup.string().email('Неверный формат email').required('Email отправителя обязателен'),
    fromName: yup.string().required('Имя отправителя обязательно')
  }),
  security: yup.object({
    sessionTimeout: yup.number().min(5, 'Минимум 5 минут').max(1440, 'Максимум 24 часа'),
    maxLoginAttempts: yup.number().min(3, 'Минимум 3 попытки').max(10, 'Максимум 10 попыток'),
    passwordMinLength: yup.number().min(6, 'Минимум 6 символов').max(50, 'Максимум 50 символов'),
    requireTwoFactor: yup.boolean(),
    allowRegistration: yup.boolean()
  }),
  storage: yup.object({
    maxFileSize: yup.number().min(1, 'Минимум 1 MB').max(100, 'Максимум 100 MB'),
    allowedFileTypes: yup.string(),
    autoCleanup: yup.boolean(),
    cleanupDays: yup.number().min(1, 'Минимум 1 день').max(365, 'Максимум 365 дней')
  })
});

const SystemSettings = ({ settings, onSave, isLoading }) => {
  const [activeSection, setActiveSection] = useState('general');

  const {
    register,
    handleSubmit,
    formState: { errors, isDirty },
    reset,
    watch
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: settings || {
      general: {
        siteName: 'Klingo Admin',
        siteDescription: 'Административная панель для изучения казахского языка',
        defaultLanguage: 'ru',
        timezone: 'Asia/Almaty',
        maintenanceMode: false
      },
      email: {
        smtpHost: 'smtp.gmail.com',
        smtpPort: 587,
        smtpUser: '',
        smtpPassword: '',
        fromEmail: '<EMAIL>',
        fromName: 'Klingo'
      },
      security: {
        sessionTimeout: 60,
        maxLoginAttempts: 5,
        passwordMinLength: 8,
        requireTwoFactor: false,
        allowRegistration: true
      },
      storage: {
        maxFileSize: 10,
        allowedFileTypes: 'jpg,jpeg,png,gif,mp3,wav,pdf',
        autoCleanup: true,
        cleanupDays: 30
      }
    }
  });

  const maintenanceMode = watch('general.maintenanceMode');

  const sections = [
    { id: 'general', label: 'Общие', icon: FiSettings },
    { id: 'email', label: 'Email', icon: FiMail },
    { id: 'security', label: 'Безопасность', icon: FiShield },
    { id: 'storage', label: 'Хранилище', icon: FiHardDrive }
  ];

  const languages = [
    { value: 'ru', label: 'Русский' },
    { value: 'kk', label: 'Қазақша' },
    { value: 'en', label: 'English' }
  ];

  const timezones = [
    { value: 'Asia/Almaty', label: 'Алматы (UTC+6)' },
    { value: 'Asia/Nur-Sultan', label: 'Нур-Султан (UTC+6)' },
    { value: 'Europe/Moscow', label: 'Москва (UTC+3)' },
    { value: 'UTC', label: 'UTC (UTC+0)' }
  ];

  const onSubmit = (data) => {
    onSave(data);
  };

  const handleReset = () => {
    reset();
    toast.success('Настройки сброшены');
  };

  const renderGeneralSettings = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Название сайта *
        </label>
        <input
          {...register('general.siteName')}
          type="text"
          className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
            errors.general?.siteName ? 'border-red-300' : 'border-gray-300'
          }`}
        />
        {errors.general?.siteName && (
          <p className="mt-1 text-sm text-red-600">{errors.general.siteName.message}</p>
        )}
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Описание сайта *
        </label>
        <textarea
          {...register('general.siteDescription')}
          rows={3}
          className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
            errors.general?.siteDescription ? 'border-red-300' : 'border-gray-300'
          }`}
        />
        {errors.general?.siteDescription && (
          <p className="mt-1 text-sm text-red-600">{errors.general.siteDescription.message}</p>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Язык по умолчанию *
          </label>
          <select
            {...register('general.defaultLanguage')}
            className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              errors.general?.defaultLanguage ? 'border-red-300' : 'border-gray-300'
            }`}
          >
            {languages.map(lang => (
              <option key={lang.value} value={lang.value}>
                {lang.label}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Часовой пояс *
          </label>
          <select
            {...register('general.timezone')}
            className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              errors.general?.timezone ? 'border-red-300' : 'border-gray-300'
            }`}
          >
            {timezones.map(tz => (
              <option key={tz.value} value={tz.value}>
                {tz.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      <div className="flex items-center">
        <input
          {...register('general.maintenanceMode')}
          type="checkbox"
          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
        <label className="ml-2 block text-sm text-gray-900">
          Режим обслуживания
        </label>
      </div>
      
      {maintenanceMode && (
        <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
          <div className="flex">
            <div className="flex-shrink-0">
              <FiClock className="h-5 w-5 text-yellow-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">
                Режим обслуживания активен
              </h3>
              <div className="mt-2 text-sm text-yellow-700">
                <p>Сайт будет недоступен для обычных пользователей. Только администраторы смогут получить доступ.</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  const renderEmailSettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            SMTP Хост *
          </label>
          <input
            {...register('email.smtpHost')}
            type="text"
            className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              errors.email?.smtpHost ? 'border-red-300' : 'border-gray-300'
            }`}
          />
          {errors.email?.smtpHost && (
            <p className="mt-1 text-sm text-red-600">{errors.email.smtpHost.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            SMTP Порт *
          </label>
          <input
            {...register('email.smtpPort', { valueAsNumber: true })}
            type="number"
            className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              errors.email?.smtpPort ? 'border-red-300' : 'border-gray-300'
            }`}
          />
          {errors.email?.smtpPort && (
            <p className="mt-1 text-sm text-red-600">{errors.email.smtpPort.message}</p>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            SMTP Пользователь *
          </label>
          <input
            {...register('email.smtpUser')}
            type="text"
            className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              errors.email?.smtpUser ? 'border-red-300' : 'border-gray-300'
            }`}
          />
          {errors.email?.smtpUser && (
            <p className="mt-1 text-sm text-red-600">{errors.email.smtpUser.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            SMTP Пароль
          </label>
          <input
            {...register('email.smtpPassword')}
            type="password"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Оставьте пустым, чтобы не изменять"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Email отправителя *
          </label>
          <input
            {...register('email.fromEmail')}
            type="email"
            className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              errors.email?.fromEmail ? 'border-red-300' : 'border-gray-300'
            }`}
          />
          {errors.email?.fromEmail && (
            <p className="mt-1 text-sm text-red-600">{errors.email.fromEmail.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Имя отправителя *
          </label>
          <input
            {...register('email.fromName')}
            type="text"
            className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              errors.email?.fromName ? 'border-red-300' : 'border-gray-300'
            }`}
          />
          {errors.email?.fromName && (
            <p className="mt-1 text-sm text-red-600">{errors.email.fromName.message}</p>
          )}
        </div>
      </div>
    </div>
  );

  const renderSecuritySettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Таймаут сессии (минуты)
          </label>
          <input
            {...register('security.sessionTimeout', { valueAsNumber: true })}
            type="number"
            min="5"
            max="1440"
            className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              errors.security?.sessionTimeout ? 'border-red-300' : 'border-gray-300'
            }`}
          />
          {errors.security?.sessionTimeout && (
            <p className="mt-1 text-sm text-red-600">{errors.security.sessionTimeout.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Максимум попыток входа
          </label>
          <input
            {...register('security.maxLoginAttempts', { valueAsNumber: true })}
            type="number"
            min="3"
            max="10"
            className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              errors.security?.maxLoginAttempts ? 'border-red-300' : 'border-gray-300'
            }`}
          />
          {errors.security?.maxLoginAttempts && (
            <p className="mt-1 text-sm text-red-600">{errors.security.maxLoginAttempts.message}</p>
          )}
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Минимальная длина пароля
        </label>
        <input
          {...register('security.passwordMinLength', { valueAsNumber: true })}
          type="number"
          min="6"
          max="50"
          className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
            errors.security?.passwordMinLength ? 'border-red-300' : 'border-gray-300'
          }`}
        />
        {errors.security?.passwordMinLength && (
          <p className="mt-1 text-sm text-red-600">{errors.security.passwordMinLength.message}</p>
        )}
      </div>

      <div className="space-y-4">
        <div className="flex items-center">
          <input
            {...register('security.requireTwoFactor')}
            type="checkbox"
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label className="ml-2 block text-sm text-gray-900">
            Требовать двухфакторную аутентификацию
          </label>
        </div>

        <div className="flex items-center">
          <input
            {...register('security.allowRegistration')}
            type="checkbox"
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label className="ml-2 block text-sm text-gray-900">
            Разрешить регистрацию новых пользователей
          </label>
        </div>
      </div>
    </div>
  );

  const renderStorageSettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Максимальный размер файла (MB)
          </label>
          <input
            {...register('storage.maxFileSize', { valueAsNumber: true })}
            type="number"
            min="1"
            max="100"
            className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              errors.storage?.maxFileSize ? 'border-red-300' : 'border-gray-300'
            }`}
          />
          {errors.storage?.maxFileSize && (
            <p className="mt-1 text-sm text-red-600">{errors.storage.maxFileSize.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Дни до автоочистки
          </label>
          <input
            {...register('storage.cleanupDays', { valueAsNumber: true })}
            type="number"
            min="1"
            max="365"
            className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              errors.storage?.cleanupDays ? 'border-red-300' : 'border-gray-300'
            }`}
          />
          {errors.storage?.cleanupDays && (
            <p className="mt-1 text-sm text-red-600">{errors.storage.cleanupDays.message}</p>
          )}
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Разрешенные типы файлов
        </label>
        <input
          {...register('storage.allowedFileTypes')}
          type="text"
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          placeholder="jpg,jpeg,png,gif,mp3,wav,pdf"
        />
        <p className="mt-1 text-sm text-gray-500">
          Разделите расширения файлов запятыми
        </p>
      </div>

      <div className="flex items-center">
        <input
          {...register('storage.autoCleanup')}
          type="checkbox"
          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
        <label className="ml-2 block text-sm text-gray-900">
          Автоматическая очистка старых файлов
        </label>
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeSection) {
      case 'general':
        return renderGeneralSettings();
      case 'email':
        return renderEmailSettings();
      case 'security':
        return renderSecuritySettings();
      case 'storage':
        return renderStorageSettings();
      default:
        return renderGeneralSettings();
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="flex">
        {/* Sidebar */}
        <div className="w-64 border-r border-gray-200">
          <div className="p-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Настройки</h3>
            <nav className="space-y-1">
              {sections.map(section => {
                const Icon = section.icon;
                return (
                  <button
                    key={section.id}
                    onClick={() => setActiveSection(section.id)}
                    className={`w-full flex items-center gap-3 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                      activeSection === section.id
                        ? 'bg-blue-100 text-blue-700'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                    }`}
                  >
                    <Icon size={16} />
                    {section.label}
                  </button>
                );
              })}
            </nav>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1">
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h4 className="text-lg font-medium text-gray-900">
                  {sections.find(s => s.id === activeSection)?.label}
                </h4>
                <div className="flex items-center gap-3">
                  <button
                    type="button"
                    onClick={handleReset}
                    disabled={!isDirty}
                    className="flex items-center gap-2 px-3 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <FiRefreshCw size={16} />
                    Сбросить
                  </button>
                  <button
                    type="submit"
                    disabled={isLoading || !isDirty}
                    className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <FiSave size={16} />
                    {isLoading ? 'Сохранение...' : 'Сохранить'}
                  </button>
                </div>
              </div>

              {renderContent()}
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default SystemSettings;
