import React, { useState, useRef } from 'react';
import { FiUpload, FiPlay, FiPause, FiX, FiVolume2 } from 'react-icons/fi';
import { validateAudioFile } from '../services/content';
import toast from 'react-hot-toast';

const AudioUpload = ({ 
  onFileSelect, 
  selectedFile, 
  onFileRemove,
  disabled = false,
  className = ""
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [audioUrl, setAudioUrl] = useState(null);
  const fileInputRef = useRef(null);
  const audioRef = useRef(null);

  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    try {
      // Validate audio file
      validateAudioFile(file);
      
      // Create preview URL
      const url = URL.createObjectURL(file);
      setAudioUrl(url);
      
      // Call parent callback
      onFileSelect(file);
      
      toast.success('Аудио файл выбран успешно');
    } catch (error) {
      toast.error(error.message);
      // Clear the input
      event.target.value = '';
    }
  };

  const handleRemoveFile = () => {
    if (audioUrl) {
      URL.revokeObjectURL(audioUrl);
      setAudioUrl(null);
    }
    setIsPlaying(false);
    onFileRemove();
    
    // Clear the input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const togglePlayback = () => {
    if (!audioRef.current) return;

    if (isPlaying) {
      audioRef.current.pause();
      setIsPlaying(false);
    } else {
      audioRef.current.play();
      setIsPlaying(true);
    }
  };

  const handleAudioEnded = () => {
    setIsPlaying(false);
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className={`space-y-3 ${className}`}>
      {/* File Input */}
      <div className="flex items-center space-x-3">
        <input
          ref={fileInputRef}
          type="file"
          accept="audio/mpeg,audio/wav,audio/ogg,audio/m4a,.mp3,.wav,.ogg,.m4a"
          onChange={handleFileSelect}
          disabled={disabled}
          className="hidden"
          id="audio-upload"
        />
        
        <label
          htmlFor="audio-upload"
          className={`
            flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-md cursor-pointer
            hover:bg-gray-50 transition-colors
            ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
            ${selectedFile ? 'border-green-300 bg-green-50' : ''}
          `}
        >
          <FiUpload size={16} />
          <span className="text-sm">
            {selectedFile ? 'Изменить аудио' : 'Выбрать аудио файл'}
          </span>
        </label>

        {selectedFile && (
          <button
            type="button"
            onClick={handleRemoveFile}
            disabled={disabled}
            className="p-2 text-red-600 hover:bg-red-50 rounded-md transition-colors"
            title="Удалить файл"
          >
            <FiX size={16} />
          </button>
        )}
      </div>

      {/* File Info */}
      {selectedFile && (
        <div className="bg-gray-50 rounded-md p-3 space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <FiVolume2 size={16} className="text-gray-500" />
              <span className="text-sm font-medium text-gray-700">
                {selectedFile.name}
              </span>
            </div>
            <span className="text-xs text-gray-500">
              {formatFileSize(selectedFile.size)}
            </span>
          </div>

          {/* Audio Player */}
          {audioUrl && (
            <div className="flex items-center space-x-3">
              <button
                type="button"
                onClick={togglePlayback}
                disabled={disabled}
                className="flex items-center justify-center w-8 h-8 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors"
              >
                {isPlaying ? <FiPause size={14} /> : <FiPlay size={14} />}
              </button>
              
              <div className="flex-1 text-xs text-gray-500">
                {isPlaying ? 'Воспроизведение...' : 'Нажмите для прослушивания'}
              </div>

              <audio
                ref={audioRef}
                src={audioUrl}
                onEnded={handleAudioEnded}
                preload="metadata"
              />
            </div>
          )}
        </div>
      )}

      {/* Help Text */}
      <div className="text-xs text-gray-500">
        Поддерживаемые форматы: MP3, WAV, OGG, M4A. Максимальный размер: 10MB
      </div>
    </div>
  );
};

export default AudioUpload;
